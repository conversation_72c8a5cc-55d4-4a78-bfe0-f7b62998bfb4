#!/usr/bin/env python3
"""
Test the API logic directly without Flask server
"""

import os
import json
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser

# Load environment variables
load_dotenv()

def simulate_api_call():
    """Simulate the API call logic directly"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test API logic")
        return
    
    print("=== Simulating API Call Logic ===\n")
    
    # Simulate the request data
    data = {
        "specifications": """
SPIRATEUR DE TRANSPORT INTRA-HOSPITALIER  - - - 
Le ventilateur d'urgence transportable doit permettre la prise en charge ventilatoire des patients 
malades pour les urgences.  
Ce respirateur doit être doté des technologies de ventilations invasive et non invasive, ainsi que 
l’oxygénothérapie et la capnographie. 
Le respirateur de transport doit être avec un système à turbine intégré qui ne nécessite pas 
l'alimentation en AIR 
Caractéristiques techniques minimales : 
Modes ventilation :  - - - - - - - - - - - 
La ventilation en volume contrôlée  
La ventilation assistée - contrôlée en pression  
La ventilation assistée - contrôlée intermittente en volume  
La ventilation assistée - contrôlée intermittente à pression contrôlée  
La ventilation à deux niveaux de pression positive Bi-Level 
La ventilation intelligente à aide adaptative 
Thérapie pour oxygène (Oxygénothérapie) 
La ventilation en pression positive à relâchements de pression  
La ventilation non invasive  
L’aide inspiratoire 
La ventilation spontanée ou PEEP 
Monitorage et surveillance : - - - - - - - 
Ecran couleur d’au moins 7 pouces intégré au respirateur avec une résolution de 640×480 pixels au 
minimum.  
Courbes en temps réel : pression, débit et volume 
Boucles : volume/pression et Débit/volume 
Pression des voies aériennes, pression Crête, pression plateau, PEP 
Oxygène, volume, résistance, activité de patient 
Monitorage du CO2 (adulte et enfant) 
Affichage de poumon dynamique avec représentation de la compliance pulmonaire 
Caractéristiques techniques : - 
Volume courant : de 2 à 2000 ml 
- - - - - - - 
Fréquence respiratoire : de 1 à 80 cycles/min minimum 
Temps inspiratoire : 0,1 à 12 Sec minimum 
Rapport I/E modulable 
FIO2 de 21% à 100% minimum 
Pression expiratoire (PEEP électronique intégré) : de 0 à 35 cm H2O minimum 
Débit inspiratoire : 0 à 260 l/min minimum 
L’aide inspiratoire : 0 à 60 cmH2O minimum 
Alarmes :  
Sonore et visuelle avec des indicateurs de la priorité (Haute, Moyenne et Basse) - 
Défaut d'alimentation en gaz. - - - - - - - - - - - 
Défaut d’alimentation électrique. 
Batterie faible (indicateur de niveau de charge sur l’écran) 
Concentration en 02 
Volume minute bas. 
Volume minute haut. 
Pression inspiratoire basse 
Pression haute. 
Apnée 
Pression élevée en permanence. 
Fréquence respiratoire haute. 
Fréquence respiratoire basse 
Autres fonctions : - - - - - - 
Nébulisation des médicaments 
Mélangeur oxygène 
Autotest du bon fonctionnement 
Compensation automatique de la sonde d'intubation 
Le respirateur doit permettre l’enregistrement des tendances de 1 à 72 heures  
Autonomie de la Batterie intégrée 240 min minimum 
L’appareil doit être livré avec : - - - - - 
Humidificateur  
Chariot mobile ergonomique d’origine  
Bras articule support circuit patient  
Accessoires complets pour nébulisation  
2 circuits patient adulte et enfant réutilisable  
- - - - - 
10 filtres anti bactériens  
10 kits pour l'oxygénothérapie 
2 Capteurs de débit adulte autoclavable 
2 Capteurs de débit enfant autoclavable 
01 Capteur CO2 réutilisable  
L'équipement doit être livré et installé avec tous les accessoires nécessaires pour garantir son bon 
fonctionnement. 
""",
        "use_embeddings": False,
        "use_chunked": False
    }
    
    print("Input data:")
    print(f"  Specifications: {data['specifications'].strip()}")
    print(f"  Use embeddings: {data['use_embeddings']}")
    print(f"  Use chunked: {data['use_chunked']}")
    print()
    
    # Initialize components (same as in app.py)
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()

    # Execute the same logic as in app.py
    specifications = data.get('specifications', '')
    use_embeddings = data.get('use_embeddings', False)
    
    if not use_embeddings:
        print("Using intelligent parsing (non-embedding mode)...")

        # Use intelligent parsing with products_specs.json knowledge
        intelligent_parser = IntelligentSpecificationParser(llm_provider, chunk_size=10)  # Test with 10 specs per chunk
        parsing_result = intelligent_parser.parse(specifications)

        # Return only parsing results (no product comparison)
        results = {
            "parsing_result": parsing_result,
            "matched_specs_count": sum(len(specs) for specs in parsing_result.get("matched_criterias", {}).values()),
            "non_matched_specs_count": len(parsing_result.get("non_matched_criterias", [])),
            "message": "Specification analysis completed. Only matched and non-matched criteria are returned."
        }

        print(f"Results structure:")
        print(f"  Matched specs count: {results['matched_specs_count']}")
        print(f"  Non-matched specs count: {results['non_matched_specs_count']}")
        print(f"  Message: {results['message']}")
        print()
    
    print("\n=== Final Results ===")
    print(f"Message: {results.get('message', 'None')}")
    print(f"Matched specs count: {results.get('matched_specs_count', 0)}")
    print(f"Non-matched specs count: {results.get('non_matched_specs_count', 0)}")
    print(f"Parsing result present: {'parsing_result' in results}")

    # Show parsing details
    parsing_result = results.get('parsing_result', {})
    if parsing_result:
        matched = parsing_result.get('matched_criterias', {})
        non_matched = parsing_result.get('non_matched_criterias', [])
        print(f"\nParsing details:")
        print(f"  Matched categories: {len(matched)}")
        for category, specs in matched.items():
            print(f"    {category}: {len(specs)} specs")
            for spec in specs[:2]:  # Show first 2 specs
                products_count = len(spec.get('products_match', {}))
                print(f"      - {spec['spec_name']}: {products_count} products")

        print(f"  Non-matched specs: {len(non_matched)}")
        for spec in non_matched[:3]:  # Show first 3 non-matched
            print(f"    - {spec['spec_name']}")

    print("\n✅ API logic simulation completed!")
    
    return results

if __name__ == "__main__":
    result = simulate_api_call()
    
    print(f"\n{'='*60}")
    print("If this simulation works, the issue might be:")
    print("1. Flask server communication")
    print("2. Frontend JavaScript processing")
    print("3. API response format")
    print(f"{'='*60}")
