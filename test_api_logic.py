#!/usr/bin/env python3
"""
Test the API logic directly without Flask server
"""

import os
import json
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser
from product_comparison.comparator import ProductComparator
from product_comparison.data_loader import load_available_products

# Load environment variables
load_dotenv()

def simulate_api_call():
    """Simulate the API call logic directly"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test API logic")
        return
    
    print("=== Simulating API Call Logic ===\n")
    
    # Simulate the request data
    data = {
        "specifications": """
SPIRATEUR DE TRANSPORT INTRA-HOSPITALIER  - - - 
Le ventilateur d'urgence transportable doit permettre la prise en charge ventilatoire des patients 
malades pour les urgences.  
Ce respirateur doit être doté des technologies de ventilations invasive et non invasive, ainsi que 
l’oxygénothérapie et la capnographie. 
Le respirateur de transport doit être avec un système à turbine intégré qui ne nécessite pas 
l'alimentation en AIR 
Caractéristiques techniques minimales : 
Modes ventilation :  - - - - - - - - - - - 
La ventilation en volume contrôlée  
La ventilation assistée - contrôlée en pression  
La ventilation assistée - contrôlée intermittente en volume  
La ventilation assistée - contrôlée intermittente à pression contrôlée  
La ventilation à deux niveaux de pression positive Bi-Level 
La ventilation intelligente à aide adaptative 
Thérapie pour oxygène (Oxygénothérapie) 
La ventilation en pression positive à relâchements de pression  
La ventilation non invasive  
L’aide inspiratoire 
La ventilation spontanée ou PEEP 
Monitorage et surveillance : - - - - - - - 
Ecran couleur d’au moins 7 pouces intégré au respirateur avec une résolution de 640×480 pixels au 
minimum.  
Courbes en temps réel : pression, débit et volume 
Boucles : volume/pression et Débit/volume 
Pression des voies aériennes, pression Crête, pression plateau, PEP 
Oxygène, volume, résistance, activité de patient 
Monitorage du CO2 (adulte et enfant) 
Affichage de poumon dynamique avec représentation de la compliance pulmonaire 
Caractéristiques techniques : - 
Volume courant : de 2 à 2000 ml 
- - - - - - - 
Fréquence respiratoire : de 1 à 80 cycles/min minimum 
Temps inspiratoire : 0,1 à 12 Sec minimum 
Rapport I/E modulable 
FIO2 de 21% à 100% minimum 
Pression expiratoire (PEEP électronique intégré) : de 0 à 35 cm H2O minimum 
Débit inspiratoire : 0 à 260 l/min minimum 
L’aide inspiratoire : 0 à 60 cmH2O minimum 
Alarmes :  
Sonore et visuelle avec des indicateurs de la priorité (Haute, Moyenne et Basse) - 
Défaut d'alimentation en gaz. - - - - - - - - - - - 
Défaut d’alimentation électrique. 
Batterie faible (indicateur de niveau de charge sur l’écran) 
Concentration en 02 
Volume minute bas. 
Volume minute haut. 
Pression inspiratoire basse 
Pression haute. 
Apnée 
Pression élevée en permanence. 
Fréquence respiratoire haute. 
Fréquence respiratoire basse 
Autres fonctions : - - - - - - 
Nébulisation des médicaments 
Mélangeur oxygène 
Autotest du bon fonctionnement 
Compensation automatique de la sonde d'intubation 
Le respirateur doit permettre l’enregistrement des tendances de 1 à 72 heures  
Autonomie de la Batterie intégrée 240 min minimum 
L’appareil doit être livré avec : - - - - - 
Humidificateur  
Chariot mobile ergonomique d’origine  
Bras articule support circuit patient  
Accessoires complets pour nébulisation  
2 circuits patient adulte et enfant réutilisable  
- - - - - 
10 filtres anti bactériens  
10 kits pour l'oxygénothérapie 
2 Capteurs de débit adulte autoclavable 
2 Capteurs de débit enfant autoclavable 
01 Capteur CO2 réutilisable  
L'équipement doit être livré et installé avec tous les accessoires nécessaires pour garantir son bon 
fonctionnement. 
""",
        "use_embeddings": False,
        "use_chunked": False
    }
    
    print("Input data:")
    print(f"  Specifications: {data['specifications'].strip()}")
    print(f"  Use embeddings: {data['use_embeddings']}")
    print(f"  Use chunked: {data['use_chunked']}")
    print()
    
    # Initialize components (same as in app.py)
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()
    available_products = load_available_products('available_products.json')
    
    print(f"Loaded {len(available_products)} products")
    print()
    
    # Execute the same logic as in app.py
    specifications = data.get('specifications', '')
    use_embeddings = data.get('use_embeddings', False)
    use_chunked = data.get('use_chunked', False)
    
    if not use_embeddings:
        print("Using intelligent parsing (non-embedding mode)...")
        
        # Use intelligent parsing with simplified_specs.json knowledge
        intelligent_parser = IntelligentSpecificationParser(llm_provider)
        parsing_result = intelligent_parser.parse(specifications)
        
        # Extract matched specifications for comparison
        parsed_specs = intelligent_parser.get_matched_specs_for_comparison(parsing_result)
        
        print(f"Parsed {len(parsed_specs)} matched specifications")
        
        # Add parsing result to the final results for user visibility
        results = {
            "parsing_result": parsing_result,
            "matched_specs_count": len(parsed_specs),
            "non_matched_specs_count": len(parsing_result.get("non_matched_criterias", []))
        }
        
        print(f"Results structure initialized:")
        print(f"  Matched specs count: {results['matched_specs_count']}")
        print(f"  Non-matched specs count: {results['non_matched_specs_count']}")
        print()
        
        # Compare with available products using matched specifications
        if parsed_specs:  # Only compare if we have matched specifications
            print("Comparing with products...")
            
            if use_chunked:
                print("Using chunked comparison...")
                # This path would use ChunkedProductComparator
            else:
                print("Using standard comparison...")
                # Fallback to standard comparison
                comparator = ProductComparator(llm_provider)
                comparison_results = comparator.compare(parsed_specs, available_products)
            
            print("Comparison completed!")
            print(f"  Best match: {comparison_results.get('best_match', 'None')}")
            print(f"  Best score: {comparison_results.get('best_match_score', 0)}%")
            print(f"  Products: {len(comparison_results.get('products', []))}")
            
            # Merge parsing results with comparison results
            results.update(comparison_results)
            
            print("\nMerged results structure:")
            print(f"  Total keys: {len(results.keys())}")
            print(f"  Keys: {list(results.keys())}")
            
        else:
            print("No matched specifications found!")
            # No matched specifications found
            results.update({
                "products": [],
                "best_match": "No matched specifications found",
                "best_match_score": 0,
                "message": "No specifications could be matched with the available product specifications. Please check the non-matched criteria."
            })
    
    print("\n=== Final Results ===")
    print(f"Best match: {results.get('best_match', 'None')}")
    print(f"Best score: {results.get('best_match_score', 0)}%")
    print(f"Products: {len(results.get('products', []))}")
    print(f"Parsing result present: {'parsing_result' in results}")
    
    # Show sample product if available
    products = results.get('products', [])
    if products:
        sample_product = products[0]
        print(f"\nSample product:")
        print(f"  Name: {sample_product.get('name', 'Unknown')}")
        print(f"  Match percentage: {sample_product.get('match_percentage', 0)}%")
        print(f"  Comparisons: {len(sample_product.get('comparisons', []))}")
    
    # Show parsing details
    parsing_result = results.get('parsing_result', {})
    if parsing_result:
        matched = parsing_result.get('matched_criterias', {})
        non_matched = parsing_result.get('non_matched_criterias', [])
        print(f"\nParsing details:")
        print(f"  Matched categories: {len(matched)}")
        print(f"  Non-matched specs: {len(non_matched)}")
    
    print("\n✅ API logic simulation completed!")
    
    return results

if __name__ == "__main__":
    result = simulate_api_call()
    
    print(f"\n{'='*60}")
    print("If this simulation works, the issue might be:")
    print("1. Flask server communication")
    print("2. Frontend JavaScript processing")
    print("3. API response format")
    print(f"{'='*60}")
