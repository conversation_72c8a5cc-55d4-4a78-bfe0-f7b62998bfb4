#!/usr/bin/env python3
"""
Debug the issue by testing components individually
"""

import os
import json
from product_comparison.data_loader import load_available_products

def test_data_loading():
    """Test if data loading works"""
    print("=== Testing Data Loading ===\n")
    
    # Test available products loading
    try:
        available_products = load_available_products('available_products.json')
        print(f"✅ Loaded {len(available_products)} products")
        
        if available_products:
            sample = available_products[0]
            print(f"   Sample product: {sample.get('Nom', 'Unknown')}")
            
            # Extract specs
            specs = {k: v for k, v in sample.items() if k not in ['Nom', 'Description']}
            print(f"   Specifications: {len(specs)}")
            
    except Exception as e:
        print(f"❌ Failed to load products: {e}")
    
    # Test simplified specs loading
    try:
        with open('simplified_specs.json', 'r', encoding='utf-8') as f:
            simplified_specs = json.load(f)
        
        print(f"✅ Loaded simplified specs")
        print(f"   Categories: {len(simplified_specs.get('groups', []))}")
        
    except Exception as e:
        print(f"❌ Failed to load simplified specs: {e}")

def test_parser_initialization():
    """Test parser initialization without API calls"""
    print("\n=== Testing Parser Initialization ===\n")
    
    try:
        from product_comparison.intelligent_parser import IntelligentSpecificationParser
        
        # Create a mock LLM provider that doesn't make API calls
        class MockLLMProvider:
            def intelligent_parse_specifications(self, text, specs):
                return {
                    "matched_criterias": {
                        "Modes de ventilation:": [
                            {
                                "spec_name": "Volume controlé",
                                "user_value": "Oui",
                                "user_input": "Volume controlé: Oui"
                            }
                        ]
                    },
                    "non_matched_criterias": [
                        {
                            "spec_name": "Custom feature",
                            "user_value": "Required",
                            "user_input": "Custom feature: Required"
                        }
                    ]
                }
        
        mock_provider = MockLLMProvider()
        parser = IntelligentSpecificationParser(mock_provider)
        
        print("✅ Parser initialized successfully")
        
        # Test parsing with mock
        result = parser.parse("Volume controlé: Oui, Custom feature: Required")
        
        matched_specs = parser.get_matched_specs_for_comparison(result)
        print(f"✅ Mock parsing works: {len(matched_specs)} matched specs")
        
    except Exception as e:
        print(f"❌ Parser initialization failed: {e}")

def test_comparator():
    """Test comparator with mock data"""
    print("\n=== Testing Comparator ===\n")
    
    try:
        from product_comparison.comparator import ProductComparator
        
        # Create a mock LLM provider
        class MockLLMProvider:
            def compare_specifications(self, user_specs, product_specs):
                return {
                    "comparisons": [
                        {
                            "criterion": "Volume controlé",
                            "user_value": "Oui",
                            "product_value": "Oui",
                            "match": True,
                            "reasoning": "Both match"
                        }
                    ],
                    "match_count": 1,
                    "total_count": 1,
                    "match_percentage": 100
                }
        
        mock_provider = MockLLMProvider()
        comparator = ProductComparator(mock_provider)
        
        # Test data
        user_specs = {"Volume controlé": "Oui"}
        available_products = load_available_products('available_products.json')
        
        if available_products:
            result = comparator.compare(user_specs, available_products[:2])  # Test with 2 products
            
            print(f"✅ Comparator works")
            print(f"   Best match: {result.get('best_match', 'None')}")
            print(f"   Products: {len(result.get('products', []))}")
        
    except Exception as e:
        print(f"❌ Comparator test failed: {e}")

def test_api_structure():
    """Test the API response structure"""
    print("\n=== Testing API Response Structure ===\n")
    
    # Simulate the expected response structure
    mock_response = {
        "parsing_result": {
            "matched_criterias": {
                "Modes de ventilation:": [
                    {
                        "spec_name": "Volume controlé",
                        "user_value": "Oui",
                        "user_input": "Volume controlé: Oui"
                    }
                ]
            },
            "non_matched_criterias": [
                {
                    "spec_name": "Custom feature",
                    "user_value": "Required",
                    "user_input": "Custom feature: Required"
                }
            ]
        },
        "matched_specs_count": 1,
        "non_matched_specs_count": 1,
        "products": [
            {
                "name": "Test Product",
                "description": "Test Description",
                "match_count": 1,
                "total_count": 1,
                "match_percentage": 100,
                "comparisons": []
            }
        ],
        "best_match": "Test Product",
        "best_match_score": 100
    }
    
    print("✅ Mock API response structure:")
    print(f"   Has parsing_result: {'parsing_result' in mock_response}")
    print(f"   Has products: {'products' in mock_response}")
    print(f"   Has best_match: {'best_match' in mock_response}")
    print(f"   Products count: {len(mock_response.get('products', []))}")
    
    # Test what the frontend would see
    parsing_result = mock_response.get('parsing_result', {})
    if parsing_result:
        matched = parsing_result.get('matched_criterias', {})
        non_matched = parsing_result.get('non_matched_criterias', [])
        print(f"   Matched categories: {len(matched)}")
        print(f"   Non-matched specs: {len(non_matched)}")

if __name__ == "__main__":
    test_data_loading()
    test_parser_initialization()
    test_comparator()
    test_api_structure()
    
    print(f"\n{'='*60}")
    print("DIAGNOSIS:")
    print("If all tests pass, the issue is likely:")
    print("1. OpenAI API timeout/error")
    print("2. Flask server communication")
    print("3. Frontend JavaScript processing")
    print(f"{'='*60}")
