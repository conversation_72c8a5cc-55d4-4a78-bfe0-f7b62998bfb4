document.addEventListener('DOMContentLoaded', function() {
    const specificationForm = document.getElementById('specificationForm');
    const resultsCard = document.getElementById('resultsCard');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const resultsContent = document.getElementById('resultsContent');
    const bestMatchProduct = document.getElementById('bestMatchProduct');
    const bestMatchScore = document.getElementById('bestMatchScore');
    const productResults = document.getElementById('productResults');
    const specificationAnalysis = document.getElementById('specificationAnalysis');
    const detailedAnalysis = document.getElementById('detailedAnalysis');

    specificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Get form values
        const specifications = document.getElementById('specifications').value;
        const useEmbeddings = document.getElementById('useEmbeddings').checked;
        const useChunked = document.getElementById('useChunked').checked;

        if (!specifications) {
            alert('Please enter product specifications');
            return;
        }

        // Show results card and loading spinner
        resultsCard.classList.remove('d-none');
        loadingSpinner.classList.remove('d-none');
        resultsContent.classList.add('d-none');

        try {
            // Send API request
            const response = await fetch('/api/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    specifications,
                    use_embeddings: useEmbeddings,
                    use_chunked: useChunked
                })
            });

            if (!response.ok) {
                throw new Error('API request failed');
            }

            const data = await response.json();

            // Display results
            displayResults(data);

            // Hide loading spinner and show results
            loadingSpinner.classList.add('d-none');
            resultsContent.classList.remove('d-none');
        } catch (error) {
            console.error('Error:', error);
            alert('An error occurred while comparing specifications. Please try again.');

            // Hide loading spinner
            loadingSpinner.classList.add('d-none');
        }
    });

    function displayResults(data) {
        // Display best match
        bestMatchProduct.textContent = data.best_match || 'No match found';
        bestMatchScore.textContent = data.best_match_score || 0;

        // Clear previous results
        productResults.innerHTML = '';
        specificationAnalysis.innerHTML = '';
        detailedAnalysis.innerHTML = '';

        // Display specification analysis (from intelligent parsing)
        if (data.parsing_result) {
            displaySpecificationAnalysis(data.parsing_result, data.matched_specs_count, data.non_matched_specs_count);
        }

        // Display selected specifications if available (from embedding-based selection)
        if (data.selected_specs && Object.keys(data.selected_specs).length > 0) {
            const selectedSpecsHtml = `
                <div class="alert alert-info mb-4">
                    <h5>Selected Specifications:</h5>
                    <div>
                        ${Object.entries(data.selected_specs).map(([category, specs]) => `
                            <div class="mb-2">
                                <strong>${category}</strong>
                                <ul class="mb-0">
                                    ${specs.map(spec => `<li>${spec}</li>`).join('')}
                                </ul>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            detailedAnalysis.innerHTML = selectedSpecsHtml;
        }

        // Display product results
        if (data.products && data.products.length > 0) {
            const productsHtml = data.products.map((product, index) => {
                return `
                    <div class="card product-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>${product.name}</h5>
                            <span class="badge bg-${getScoreBadgeColor(product.match_percentage)}">${product.match_percentage}%</span>
                        </div>
                        <div class="card-body">
                            <p><strong>Description:</strong> ${product.description || 'N/A'}</p>
                            <p><strong>Matches:</strong> ${product.match_count} / ${product.total_count}</p>
                            <button class="btn btn-sm btn-outline-primary product-details"
                                    onclick="toggleProductDetails(${index})">
                                Show Details
                            </button>
                            <div id="productDetails${index}" class="mt-3 d-none">
                                ${getComparisonDetails(product.comparisons)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            productResults.innerHTML = productsHtml;

            // Add toggle function to window
            window.toggleProductDetails = function(index) {
                const detailsElement = document.getElementById(`productDetails${index}`);
                const button = detailsElement.previousElementSibling;

                if (detailsElement.classList.contains('d-none')) {
                    detailsElement.classList.remove('d-none');
                    button.textContent = 'Hide Details';
                } else {
                    detailsElement.classList.add('d-none');
                    button.textContent = 'Show Details';
                }
            };
        } else {
            productResults.innerHTML = '<div class="alert alert-warning">No product comparison results available</div>';
        }

        // Display detailed analysis
        if (data.detailed_analysis && Object.keys(data.detailed_analysis).length > 0) {
            const analysisHtml = `
                <div class="alert alert-info">
                    <p>Detailed analysis is available in the product comparison results above.</p>
                </div>
            `;

            detailedAnalysis.innerHTML = analysisHtml;
        } else {
            detailedAnalysis.innerHTML = '<div class="alert alert-warning">No detailed analysis available</div>';
        }
    }

    function getComparisonDetails(comparisons) {
        if (!comparisons || comparisons.length === 0) {
            return '<div class="alert alert-warning">No comparison details available</div>';
        }

        return comparisons.map(comparison => {
            const matchClass = comparison.match ? 'comparison-match' : 'comparison-mismatch';
            const matchIcon = comparison.match ? '✓' : '✗';

            return `
                <div class="comparison-item ${matchClass}">
                    <div class="d-flex justify-content-between">
                        <strong>${comparison.criterion}</strong>
                        <span>${matchIcon}</span>
                    </div>
                    <div class="mt-2">
                        <small><strong>User Value:</strong> ${comparison.user_value || 'N/A'}</small><br>
                        <small><strong>Product Value:</strong> ${comparison.product_value || 'N/A'}</small>
                    </div>
                    <div class="mt-2">
                        <small>${comparison.reasoning || 'No reasoning provided'}</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    function getScoreBadgeColor(score) {
        if (score >= 80) return 'success';
        if (score >= 60) return 'info';
        if (score >= 40) return 'warning';
        return 'danger';
    }

    function displaySpecificationAnalysis(parsingResult, matchedCount, nonMatchedCount) {
        const matchedCriterias = parsingResult.matched_criterias || {};
        const nonMatchedCriterias = parsingResult.non_matched_criterias || [];

        let analysisHtml = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white">
                            <h6 class="mb-0">✅ Matched Specifications (${matchedCount})</h6>
                        </div>
                        <div class="card-body">
        `;

        if (Object.keys(matchedCriterias).length > 0) {
            for (const [category, specs] of Object.entries(matchedCriterias)) {
                analysisHtml += `
                    <div class="mb-4">
                        <strong class="text-success">${category}</strong>
                        <div class="ms-3">
                `;
                for (const spec of specs) {
                    const productsEvaluation = spec.products_evaluation || [];
                    const fulfillsCount = productsEvaluation.filter(p => p.fulfills_requirement).length;
                    const totalProducts = productsEvaluation.length;

                    analysisHtml += `
                        <div class="card mb-3 border-success">
                            <div class="card-header bg-light">
                                <h6 class="mb-0">
                                    <span class="badge bg-success me-2">${spec.spec_name}</span>
                                    <span class="badge bg-info">${fulfillsCount}/${totalProducts} products fulfill</span>
                                </h6>
                                <small class="text-muted">Requirement: ${spec.cps_spec_requirement}</small>
                            </div>
                            <div class="card-body">
                                <div class="row">
                    `;

                    for (const product of productsEvaluation) {
                        const badgeClass = product.fulfills_requirement ? 'bg-success' : 'bg-danger';
                        const icon = product.fulfills_requirement ? '✓' : '✗';

                        analysisHtml += `
                            <div class="col-md-6 mb-2">
                                <div class="border rounded p-2">
                                    <div class="d-flex align-items-center mb-1">
                                        <span class="badge ${badgeClass} me-2">${icon}</span>
                                        <strong>${product.product_name}</strong>
                                    </div>
                                    <small class="text-muted d-block">Value: ${product.product_value}</small>
                                    <small class="text-muted">${product.reasoning}</small>
                                </div>
                            </div>
                        `;
                    }

                    analysisHtml += `
                                </div>
                            </div>
                        </div>
                    `;
                }
                analysisHtml += `</div></div>`;
            }
        } else {
            analysisHtml += `<p class="text-muted">No specifications matched with the available product specifications.</p>`;
        }

        analysisHtml += `
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-dark">
                            <h6 class="mb-0">⚠️ Non-Matched Specifications (${nonMatchedCount})</h6>
                        </div>
                        <div class="card-body">
        `;

        if (nonMatchedCriterias.length > 0) {
            analysisHtml += `<ul class="list-unstyled">`;
            for (const spec of nonMatchedCriterias) {
                analysisHtml += `
                    <li class="mb-2">
                        <span class="badge bg-warning text-dark me-2">${spec.spec_name}</span>
                        <small class="text-muted d-block">${spec.user_value}</small>
                        <small class="text-muted fst-italic">"${spec.user_input}"</small>
                    </li>
                `;
            }
            analysisHtml += `</ul>`;
        } else {
            analysisHtml += `<p class="text-muted">All specifications were successfully matched!</p>`;
        }

        analysisHtml += `
                        </div>
                    </div>
                </div>
            </div>
        `;

        specificationAnalysis.innerHTML = analysisHtml;
    }
});
