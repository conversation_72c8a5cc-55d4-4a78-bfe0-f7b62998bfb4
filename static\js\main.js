document.addEventListener('DOMContentLoaded', function() {
    const specificationForm = document.getElementById('specificationForm');
    const resultsCard = document.getElementById('resultsCard');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const resultsContent = document.getElementById('resultsContent');
    const bestMatchProduct = document.getElementById('bestMatchProduct');
    const bestMatchScore = document.getElementById('bestMatchScore');
    const productResults = document.getElementById('productResults');
    const detailedAnalysis = document.getElementById('detailedAnalysis');

    specificationForm.addEventListener('submit', async function(e) {
        e.preventDefault();

        // Get form values
        const specifications = document.getElementById('specifications').value;
        const useEmbeddings = document.getElementById('useEmbeddings').checked;
        const useChunked = document.getElementById('useChunked').checked;

        if (!specifications) {
            alert('Please enter product specifications');
            return;
        }

        // Show results card and loading spinner
        resultsCard.classList.remove('d-none');
        loadingSpinner.classList.remove('d-none');
        resultsContent.classList.add('d-none');

        try {
            // Send API request
            const response = await fetch('/api/compare', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    specifications,
                    use_embeddings: useEmbeddings,
                    use_chunked: useChunked
                })
            });

            if (!response.ok) {
                throw new Error('API request failed');
            }

            const data = await response.json();

            // Display results
            displayResults(data);

            // Hide loading spinner and show results
            loadingSpinner.classList.add('d-none');
            resultsContent.classList.remove('d-none');
        } catch (error) {
            console.error('Error:', error);
            alert('An error occurred while comparing specifications. Please try again.');

            // Hide loading spinner
            loadingSpinner.classList.add('d-none');
        }
    });

    function displayResults(data) {
        // Display best match
        bestMatchProduct.textContent = data.best_match || 'No match found';
        bestMatchScore.textContent = data.best_match_score || 0;

        // Clear previous results
        productResults.innerHTML = '';
        detailedAnalysis.innerHTML = '';

        // Display selected specifications if available (from embedding-based selection)
        if (data.selected_specs && Object.keys(data.selected_specs).length > 0) {
            const selectedSpecsHtml = `
                <div class="alert alert-info mb-4">
                    <h5>Selected Specifications:</h5>
                    <div>
                        ${Object.entries(data.selected_specs).map(([category, specs]) => `
                            <div class="mb-2">
                                <strong>${category}</strong>
                                <ul class="mb-0">
                                    ${specs.map(spec => `<li>${spec}</li>`).join('')}
                                </ul>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
            detailedAnalysis.innerHTML = selectedSpecsHtml;
        }

        // Display product results
        if (data.products && data.products.length > 0) {
            const productsHtml = data.products.map((product, index) => {
                return `
                    <div class="card product-card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5>${product.name}</h5>
                            <span class="badge bg-${getScoreBadgeColor(product.match_percentage)}">${product.match_percentage}%</span>
                        </div>
                        <div class="card-body">
                            <p><strong>Description:</strong> ${product.description || 'N/A'}</p>
                            <p><strong>Matches:</strong> ${product.match_count} / ${product.total_count}</p>
                            <button class="btn btn-sm btn-outline-primary product-details"
                                    onclick="toggleProductDetails(${index})">
                                Show Details
                            </button>
                            <div id="productDetails${index}" class="mt-3 d-none">
                                ${getComparisonDetails(product.comparisons)}
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            productResults.innerHTML = productsHtml;

            // Add toggle function to window
            window.toggleProductDetails = function(index) {
                const detailsElement = document.getElementById(`productDetails${index}`);
                const button = detailsElement.previousElementSibling;

                if (detailsElement.classList.contains('d-none')) {
                    detailsElement.classList.remove('d-none');
                    button.textContent = 'Hide Details';
                } else {
                    detailsElement.classList.add('d-none');
                    button.textContent = 'Show Details';
                }
            };
        } else {
            productResults.innerHTML = '<div class="alert alert-warning">No product comparison results available</div>';
        }

        // Display detailed analysis
        if (data.detailed_analysis && Object.keys(data.detailed_analysis).length > 0) {
            const analysisHtml = `
                <div class="alert alert-info">
                    <p>Detailed analysis is available in the product comparison results above.</p>
                </div>
            `;

            detailedAnalysis.innerHTML = analysisHtml;
        } else {
            detailedAnalysis.innerHTML = '<div class="alert alert-warning">No detailed analysis available</div>';
        }
    }

    function getComparisonDetails(comparisons) {
        if (!comparisons || comparisons.length === 0) {
            return '<div class="alert alert-warning">No comparison details available</div>';
        }

        return comparisons.map(comparison => {
            const matchClass = comparison.match ? 'comparison-match' : 'comparison-mismatch';
            const matchIcon = comparison.match ? '✓' : '✗';

            return `
                <div class="comparison-item ${matchClass}">
                    <div class="d-flex justify-content-between">
                        <strong>${comparison.criterion}</strong>
                        <span>${matchIcon}</span>
                    </div>
                    <div class="mt-2">
                        <small><strong>User Value:</strong> ${comparison.user_value || 'N/A'}</small><br>
                        <small><strong>Product Value:</strong> ${comparison.product_value || 'N/A'}</small>
                    </div>
                    <div class="mt-2">
                        <small>${comparison.reasoning || 'No reasoning provided'}</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    function getScoreBadgeColor(score) {
        if (score >= 80) return 'success';
        if (score >= 60) return 'info';
        if (score >= 40) return 'warning';
        return 'danger';
    }
});
