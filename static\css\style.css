/* Custom styles */
.card {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
    background-color: #f8f9fa;
}

textarea {
    resize: vertical;
}

.product-card {
    margin-bottom: 20px;
}

.comparison-item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 5px;
}

.comparison-match {
    background-color: rgba(40, 167, 69, 0.1);
    border-left: 4px solid #28a745;
}

.comparison-mismatch {
    background-color: rgba(220, 53, 69, 0.1);
    border-left: 4px solid #dc3545;
}

.product-score {
    font-size: 1.2rem;
    font-weight: bold;
}

.product-details {
    cursor: pointer;
}

.product-details:hover {
    text-decoration: underline;
}
