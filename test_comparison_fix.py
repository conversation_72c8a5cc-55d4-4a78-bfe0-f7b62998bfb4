#!/usr/bin/env python3
"""
Test script to verify the comparison fix works correctly
"""

import os
import json
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser
from product_comparison.comparator import ProductComparator
from product_comparison.data_loader import load_available_products

# Load environment variables
load_dotenv()

def test_comparison_flow():
    """Test the complete comparison flow"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test comparison")
        return
    
    print("=== Testing Complete Comparison Flow ===\n")
    
    # Initialize components
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()
    parser = IntelligentSpecificationParser(llm_provider)
    comparator = ProductComparator(llm_provider)
    
    # Load available products
    available_products = load_available_products('available_products.json')
    print(f"Loaded {len(available_products)} products")
    
    # Test input
    test_input = """
    Volume controlé: Oui
    Pression controlée: <PERSON><PERSON>
    <PERSON><PERSON> (AI): Oui
    HFO: Non
    """
    
    print(f"Test input: {test_input.strip()}")
    print()
    
    # Step 1: Parse specifications
    print("Step 1: Parsing specifications...")
    parsing_result = parser.parse(test_input)
    
    matched_specs = parser.get_matched_specs_for_comparison(parsing_result)
    print(f"Matched specifications: {len(matched_specs)}")
    for spec, value in matched_specs.items():
        print(f"  {spec}: {value}")
    print()
    
    # Step 2: Compare with products
    if matched_specs:
        print("Step 2: Comparing with products...")
        comparison_results = comparator.compare(matched_specs, available_products)
        
        print(f"Best match: {comparison_results.get('best_match', 'None')}")
        print(f"Best match score: {comparison_results.get('best_match_score', 0)}%")
        print(f"Products compared: {len(comparison_results.get('products', []))}")
        
        # Show top 3 products
        products = comparison_results.get('products', [])
        if products:
            print("\nTop 3 products:")
            sorted_products = sorted(products, key=lambda x: x.get('match_percentage', 0), reverse=True)
            for i, product in enumerate(sorted_products[:3], 1):
                print(f"  {i}. {product['name']}: {product['match_percentage']}% ({product['match_count']}/{product['total_count']} matches)")
        
        print("✅ Comparison completed successfully")
    else:
        print("❌ No matched specifications found for comparison")

def test_product_data_structure():
    """Test the product data structure"""
    print("\n=== Testing Product Data Structure ===\n")
    
    # Load and examine product data
    available_products = load_available_products('available_products.json')
    
    if available_products:
        sample_product = available_products[0]
        print(f"Sample product: {sample_product.get('Nom', 'Unknown')}")
        print(f"Description: {sample_product.get('Description', 'No description')}")
        
        # Extract specifications (excluding Nom and Description)
        product_specs = {k: v for k, v in sample_product.items() 
                        if k not in ['Nom', 'Description']}
        
        print(f"Specifications count: {len(product_specs)}")
        print("Sample specifications:")
        for i, (spec, value) in enumerate(list(product_specs.items())[:5], 1):
            print(f"  {i}. {spec}: {value}")
        
        print("✅ Product data structure is correct")
    else:
        print("❌ No products loaded")

def test_llm_comparison():
    """Test the LLM comparison directly"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test LLM comparison")
        return
    
    print("\n=== Testing LLM Comparison Directly ===\n")
    
    # Initialize LLM provider
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()
    
    # Test data
    user_specs = {
        "Volume controlé": "Oui",
        "Pression controlée": "Oui",
        "HFO": "Non"
    }
    
    product_specs = {
        "Volume controlé": "Oui",
        "Pression controlée": "Oui",
        "Aide inspiratoire (AI)": "Oui",
        "HFO": "Non"
    }
    
    print("User specifications:")
    for spec, value in user_specs.items():
        print(f"  {spec}: {value}")
    
    print("\nProduct specifications:")
    for spec, value in product_specs.items():
        print(f"  {spec}: {value}")
    
    print("\nCalling LLM comparison...")
    try:
        result = llm_provider.compare_specifications(user_specs, product_specs)
        
        print(f"Match count: {result.get('match_count', 0)}")
        print(f"Total count: {result.get('total_count', 0)}")
        print(f"Match percentage: {result.get('match_percentage', 0)}%")
        
        comparisons = result.get('comparisons', [])
        if comparisons:
            print("\nDetailed comparisons:")
            for comp in comparisons[:3]:  # Show first 3
                print(f"  {comp.get('criterion', 'Unknown')}: {comp.get('match', False)} - {comp.get('reasoning', 'No reasoning')}")
        
        print("✅ LLM comparison works correctly")
        
    except Exception as e:
        print(f"❌ LLM comparison failed: {e}")

if __name__ == "__main__":
    test_product_data_structure()
    test_llm_comparison()
    test_comparison_flow()
    
    print(f"\n{'='*60}")
    print("If all tests pass, the comparison should work in the web app!")
    print(f"{'='*60}")
