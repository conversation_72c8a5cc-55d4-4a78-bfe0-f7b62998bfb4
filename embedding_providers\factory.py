from .openai_embeddings import OpenAIEmbeddingProvider

class EmbeddingProviderFactory:
    """Factory for creating OpenAI embedding providers"""

    def get_provider(self, provider_name=None):
        """
        Get an OpenAI embedding provider (only OpenAI is supported)

        Args:
            provider_name (str): Ignored - only OpenAI is supported

        Returns:
            OpenAIEmbeddingProvider: An instance of the OpenAI embedding provider
        """
        return OpenAIEmbeddingProvider()
