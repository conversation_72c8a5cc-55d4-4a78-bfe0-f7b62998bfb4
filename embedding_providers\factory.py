from .openai_embeddings import OpenAIEmbeddingProvider
from .sentence_transformer_embeddings import SentenceTransformerEmbeddingProvider

class EmbeddingProviderFactory:
    """Factory for creating embedding providers"""
    
    def get_provider(self, provider_name):
        """
        Get an embedding provider by name
        
        Args:
            provider_name (str): The name of the provider ('openai' or 'sentence_transformer')
            
        Returns:
            BaseEmbeddingProvider: An instance of the requested provider
        """
        if provider_name.lower() == 'openai':
            return OpenAIEmbeddingProvider()
        elif provider_name.lower() in ['sentence_transformer', 'sentence_transformers', 'st']:
            return SentenceTransformerEmbeddingProvider()
        else:
            raise ValueError(f"Unknown embedding provider: {provider_name}")
