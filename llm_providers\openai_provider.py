import os
import json
from openai import OpenAI
from .base_provider import BaseLLMProvider

class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider implementation with maximum capabilities"""

    def __init__(self, model="gpt-4.1-mini"):
        """
        Initialize OpenAI provider with the most powerful model

        Args:
            model (str): The OpenAI model to use (default: gpt-4.1-mini for maximum performance)
        """
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")

        self.model = model
        self.client = OpenAI(api_key=self.api_key)

        # Set maximum completion token limits based on model (these are output tokens, not context)
        self.max_tokens_map = {
            "gpt-4.1-mini": 16384,  # GPT-4.1-mini max completion tokens (128K context)
            "gpt-4.1-nano": 16384,  # GPT-4.1-nano max completion tokens (128K context)
            "gpt-4o": 16384,        # GPT-4o max completion tokens
            "gpt-4o-mini": 16384,   # GPT-4o-mini max completion tokens
            "gpt-4-turbo": 4096,    # GPT-4 Turbo max completion tokens
            "gpt-4": 4096,          # GPT-4 max completion tokens
            "gpt-3.5-turbo": 4096   # GPT-3.5 Turbo max completion tokens
        }

    def generate_response(self, prompt, temperature=0, max_tokens=None):
        """
        Generate a response from OpenAI with maximum capabilities

        Args:
            prompt (str): The prompt to send to the LLM
            temperature (float): The temperature for generation (default: 0 for more deterministic outputs)
            max_tokens (int): Maximum number of tokens to generate (default: use model maximum)

        Returns:
            str: The generated response
        """
        # Use model maximum if no max_tokens specified
        if max_tokens is None:
            max_tokens = self.max_tokens_map.get(self.model, 16384)

        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            response_format={ "type": "json_object" },
            max_tokens=max_tokens
        )

        return response.choices[0].message.content

    def parse_specifications(self, specifications_text):
        """
        Parse product specifications text into structured format using OpenAI

        Args:
            specifications_text (str): Raw specifications text

        Returns:
            dict: Parsed specifications
        """
        prompt = f"""
        Parse the following product specifications into a structured format.
        Extract each specification criterion and its value.

        Specifications:
        {specifications_text}

        Return the result as a JSON object with criteria as keys and values as values.
        Only include criteria that are explicitly mentioned in the specifications.
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse the text manually
                parsed_specs = {}
                lines = specifications_text.strip().split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        parsed_specs[key.strip()] = value.strip()
                return parsed_specs
        except Exception as e:
            print(f"Error parsing specifications: {e}")
            return {}

    def compare_specifications(self, parsed_specs, product_specs):
        """
        Compare parsed specifications with a product's specifications using OpenAI

        Args:
            parsed_specs (dict): Parsed specifications
            product_specs (dict): Product specifications

        Returns:
            dict: Comparison results with reasoning
        """
        prompt = f"""
        Compare the following specifications with a product's specifications.

        User Specifications:
        {json.dumps(parsed_specs, ensure_ascii=False, indent=2)}

        Product Specifications:
        {json.dumps(product_specs, ensure_ascii=False, indent=2)}

        For each criterion in the user specifications, determine if it matches the product specifications.
        Provide detailed reasoning for each comparison.

        Return the result as a JSON object with the following structure:
        {{
            "comparisons": [
                {{
                    "criterion": "criterion_name",
                    "user_value": "user_value",
                    "product_value": "product_value",
                    "match": true/false,
                    "reasoning": "detailed reasoning"
                }},
                ...
            ],
            "match_count": number_of_matches,
            "total_count": total_number_of_criteria,
            "match_percentage": percentage_of_matches
        }}
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, return a basic structure
                return {
                    "comparisons": [],
                    "match_count": 0,
                    "total_count": len(parsed_specs),
                    "match_percentage": 0
                }
        except Exception as e:
            print(f"Error comparing specifications: {e}")
            return {
                "comparisons": [],
                "match_count": 0,
                "total_count": len(parsed_specs),
                "match_percentage": 0,
                "error": str(e)
            }
