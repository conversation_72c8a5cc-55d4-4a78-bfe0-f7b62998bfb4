import os
import json
from openai import OpenAI
from .base_provider import BaseLLMProvider

class OpenAIProvider(BaseLLMProvider):
    """OpenAI LLM provider implementation with maximum capabilities"""

    def __init__(self, model="gpt-4.1-mini"):
        """
        Initialize OpenAI provider with the most powerful model

        Args:
            model (str): The OpenAI model to use (default: gpt-4.1-mini for maximum performance)
        """
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")

        self.model = model
        self.client = OpenAI(api_key=self.api_key)

        # Set maximum completion token limits based on model (these are output tokens, not context)
        self.max_tokens_map = {
            "gpt-4.1-mini": 16384,  # GPT-4.1-mini max completion tokens (128K context)
            "gpt-4.1-nano": 16384,  # GPT-4.1-nano max completion tokens (128K context)
            "gpt-4o": 16384,        # GPT-4o max completion tokens
            "gpt-4o-mini": 16384,   # GPT-4o-mini max completion tokens
            "gpt-4-turbo": 4096,    # GPT-4 Turbo max completion tokens
            "gpt-4": 4096,          # GPT-4 max completion tokens
            "gpt-3.5-turbo": 4096   # GPT-3.5 Turbo max completion tokens
        }

    def generate_response(self, prompt, temperature=0, max_tokens=None):
        """
        Generate a response from OpenAI with maximum capabilities

        Args:
            prompt (str): The prompt to send to the LLM
            temperature (float): The temperature for generation (default: 0 for more deterministic outputs)
            max_tokens (int): Maximum number of tokens to generate (default: use model maximum)

        Returns:
            str: The generated response
        """
        # Use model maximum if no max_tokens specified
        if max_tokens is None:
            max_tokens = self.max_tokens_map.get(self.model, 16384)

        response = self.client.chat.completions.create(
            model=self.model,
            messages=[{"role": "user", "content": prompt}],
            temperature=temperature,
            response_format={ "type": "json_object" },
            max_tokens=max_tokens
        )

        return response.choices[0].message.content

    def parse_specifications(self, specifications_text):
        """
        Parse product specifications text into structured format using OpenAI

        Args:
            specifications_text (str): Raw specifications text

        Returns:
            dict: Parsed specifications
        """
        prompt = f"""
        Parse the following product specifications into a structured format.
        Extract each specification criterion and its value.

        Specifications:
        {specifications_text}

        Return the result as a JSON object with criteria as keys and values as values.
        Only include criteria that are explicitly mentioned in the specifications.
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse the text manually
                parsed_specs = {}
                lines = specifications_text.strip().split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        parsed_specs[key.strip()] = value.strip()
                return parsed_specs
        except Exception as e:
            print(f"Error parsing specifications: {e}")
            return {}

    def intelligent_parse_specifications(self, specifications_text, products_specs_content, chunk_size=10, include_reasoning=False):
        """
        Intelligently parse product specifications using OpenAI with knowledge of available specs

        Args:
            specifications_text (str): Raw specifications text from user
            products_specs_content (dict): Content from products_specs.json
            chunk_size (int): Number of specifications per API call (default: 10, -1 for all)
            include_reasoning (bool): Whether to include reasoning in output (default: False)

        Returns:
            dict: Dictionary with 'matched_criterias' and 'non_matched_criterias'
        """
        if chunk_size == -1:
            # Process all specifications in one call
            return self._process_specifications_chunk(specifications_text, products_specs_content, None, include_reasoning)
        else:
            # Process specifications in chunks
            return self._process_specifications_in_chunks(specifications_text, products_specs_content, chunk_size, include_reasoning)

    def _process_specifications_in_chunks(self, specifications_text, products_specs_content, chunk_size, include_reasoning=False):
        """
        Process specifications in chunks to avoid API overload

        Args:
            specifications_text (str): Raw specifications text from user
            products_specs_content (dict): Content from products_specs.json
            chunk_size (int): Number of specifications per chunk
            include_reasoning (bool): Whether to include reasoning in output

        Returns:
            dict: Combined results from all chunks
        """
        combined_results = {
            "matched_criterias": {},
            "non_matched_criterias": []
        }

        # Process each group
        for group_dict in products_specs_content.get('groups', []):
            for category_name, specs_list in group_dict.items():
                print(f"Processing category: {category_name}")

                # Split specifications into chunks
                chunks = [specs_list[i:i + chunk_size] for i in range(0, len(specs_list), chunk_size)]

                for chunk_idx, chunk in enumerate(chunks):
                    print(f"  Processing chunk {chunk_idx + 1}/{len(chunks)} ({len(chunk)} specs)")

                    # Create a temporary products_specs structure for this chunk
                    chunk_products_specs = {
                        "groups": [{category_name: chunk}]
                    }

                    # Process this chunk
                    chunk_result = self._process_specifications_chunk(
                        specifications_text,
                        chunk_products_specs,
                        f"chunk {chunk_idx + 1} of {category_name}",
                        include_reasoning
                    )

                    # Merge results
                    self._merge_chunk_results(combined_results, chunk_result)

        return combined_results

    def _process_specifications_chunk(self, specifications_text, products_specs_content, chunk_info=None, include_reasoning=False):
        """
        Process a chunk of specifications

        Args:
            specifications_text (str): Raw specifications text from user
            products_specs_content (dict): Content from products_specs.json (or chunk)
            chunk_info (str): Information about the current chunk
            include_reasoning (bool): Whether to include reasoning in output

        Returns:
            dict: Dictionary with 'matched_criterias' and 'non_matched_criterias'
        """
        # Convert products_specs to a readable format for the LLM
        available_specs_text = self._format_products_specs_for_llm(products_specs_content)

        chunk_info_text = f" (Processing {chunk_info})" if chunk_info else ""

        # Build the output structure based on include_reasoning
        if include_reasoning:
            output_structure = '''
        {{
            "matched_criterias": {{
                "category_name": [
                    {{
                        "spec_name": "exact name from available specs",
                        "cps_spec_requirement": "exact customer requirement copied as-is from CPS input",
                        "products_evaluation": [
                            {{
                                "product_name": "product name",
                                "product_value": "product's specification value",
                                "fulfills_requirement": true/false,
                                "reasoning": "detailed explanation of why it does/doesn't fulfill the requirement"
                            }}
                        ]
                    }}
                ]
            }},
            "non_matched_criterias": [
                {{
                    "spec_name": "specification name not in available list",
                    "cps_spec_requirement": "exact customer requirement copied as-is from CPS input"
                }}
            ]
        }}'''
        else:
            output_structure = '''
        {{
            "matched_criterias": {{
                "category_name": [
                    {{
                        "spec_name": "exact name from available specs",
                        "cps_spec_requirement": "exact customer requirement copied as-is from CPS input",
                        "products_evaluation": [
                            {{
                                "product_name": "product name",
                                "product_value": "product's specification value",
                                "fulfills_requirement": true/false
                            }}
                        ]
                    }}
                ]
            }},
            "non_matched_criterias": [
                {{
                    "spec_name": "specification name not in available list",
                    "cps_spec_requirement": "exact customer requirement copied as-is from CPS input"
                }}
            ]
        }}'''

        prompt = f"""
        You are an expert in medical ventilator specifications. Your task is to analyze customer product specifications (CPS) and categorize them based on available product specifications{chunk_info_text}.

        AVAILABLE SPECIFICATIONS (from products_specs.json):
        {available_specs_text}

        CUSTOMER PRODUCT SPECIFICATIONS TO ANALYZE:
        {specifications_text}

        Your task:
        1. Extract all specifications mentioned in the customer input
        2. For each extracted specification, determine if it matches (exactly or semantically) any specification from the available list
        3. Use the EXACT names from the available specifications list when there's a match
        4. For matched specifications, evaluate ALL products (no products should be missing)
        5. Categorize specifications into matched and non-matched

        Return a JSON object with this exact structure:
        {output_structure}

        Rules:
        - Use exact category names from the available specifications
        - Use exact specification names from the available specifications when there's a match
        - Be intelligent about semantic matching (e.g., "Volume Control" matches "Volume controlé")
        - Copy the customer's requirement EXACTLY as written in the CPS input for cps_spec_requirement
        - ALWAYS include ALL products for each matched specification (no products should be missing)
        - ALWAYS include ALL matched specifications from the available specs (complete coverage)
        - For each product, evaluate if it fulfills the customer's requirement (True/False)
        - Only process specifications that are relevant to this chunk

        Evaluation Guidelines:
        - Compare the customer's requirement with each product's specification value
        - Consider ranges, compatibility, and functional equivalence
        - Be strict but fair in evaluation
        - Always consider detailed reasoning internally but don't include it in output unless specifically requested
        - Ensure every product in the specification data is evaluated
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, return empty structure
                return {
                    "matched_criterias": {},
                    "non_matched_criterias": []
                }
        except Exception as e:
            print(f"Error in intelligent parsing: {e}")
            return {
                "matched_criterias": {},
                "non_matched_criterias": []
            }

    def _merge_chunk_results(self, combined_results, chunk_result):
        """
        Merge chunk results into combined results

        Args:
            combined_results (dict): Combined results to merge into
            chunk_result (dict): Results from current chunk
        """
        # Merge matched criterias
        chunk_matched = chunk_result.get("matched_criterias", {})
        for category, specs in chunk_matched.items():
            if category not in combined_results["matched_criterias"]:
                combined_results["matched_criterias"][category] = []
            combined_results["matched_criterias"][category].extend(specs)

        # Merge non-matched criterias (avoid duplicates)
        chunk_non_matched = chunk_result.get("non_matched_criterias", [])
        existing_non_matched = {spec.get("spec_name", "") for spec in combined_results["non_matched_criterias"]}

        for spec in chunk_non_matched:
            spec_name = spec.get("spec_name", "")
            if spec_name not in existing_non_matched:
                combined_results["non_matched_criterias"].append(spec)
                existing_non_matched.add(spec_name)

    def _format_products_specs_for_llm(self, products_specs_content):
        """
        Format products specs content for LLM consumption

        Args:
            products_specs_content (dict): Content from products_specs.json

        Returns:
            str: Formatted text for LLM
        """
        formatted_text = ""

        for group_dict in products_specs_content.get('groups', []):
            for category_name, specs_list in group_dict.items():
                formatted_text += f"\n{category_name}\n"
                for spec_item in specs_list:
                    spec_name = spec_item.get('Specifications', 'Unknown')
                    formatted_text += f"  - {spec_name}\n"

                    # Add product values for reference
                    product_values = []
                    for key, value in spec_item.items():
                        if key != 'Specifications' and value and value not in ['****', '*****', 'null']:
                            product_values.append(f"{key}: {value}")

                    if product_values:
                        formatted_text += f"    Products: {'; '.join(product_values[:])}{'...' if len(product_values) > 3 else ''}\n"
                        #set it to 3 if you want specific products
        return formatted_text

    def compare_specifications(self, parsed_specs, product_specs):
        """
        Compare parsed specifications with a product's specifications using OpenAI

        Args:
            parsed_specs (dict): Parsed specifications
            product_specs (dict): Product specifications

        Returns:
            dict: Comparison results with reasoning
        """
        prompt = f"""
        Compare the following specifications with a product's specifications.

        User Specifications:
        {json.dumps(parsed_specs, ensure_ascii=False, indent=2)}

        Product Specifications:
        {json.dumps(product_specs, ensure_ascii=False, indent=2)}

        For each criterion in the user specifications, determine if it matches the product specifications.
        Provide detailed reasoning for each comparison.

        Return the result as a JSON object with the following structure:
        {{
            "comparisons": [
                {{
                    "criterion": "criterion_name",
                    "user_value": "user_value",
                    "product_value": "product_value",
                    "match": true/false,
                    "reasoning": "detailed reasoning"
                }},
                ...
            ],
            "match_count": number_of_matches,
            "total_count": total_number_of_criteria,
            "match_percentage": percentage_of_matches
        }}
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, return a basic structure
                return {
                    "comparisons": [],
                    "match_count": 0,
                    "total_count": len(parsed_specs),
                    "match_percentage": 0
                }
        except Exception as e:
            print(f"Error comparing specifications: {e}")
            return {
                "comparisons": [],
                "match_count": 0,
                "total_count": len(parsed_specs),
                "match_percentage": 0,
                "error": str(e)
            }
