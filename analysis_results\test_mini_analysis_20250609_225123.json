{"parsing_result": {"matched_criterias": {"Modes de ventilation": [{"spec_name": "Volume controlé", "cps_spec_requirement": "La ventilation en volume contrôlée", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Pression controlée", "cps_spec_requirement": "La ventilation assistée - contrôlée en pression", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Assistée - controlée intermittente en volume", "cps_spec_requirement": "La ventilation assistée - contrôlée intermittente en volume", "products_fulfills_requirement": ["EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Assistée - controlée intermittente en pression", "cps_spec_requirement": "La ventilation assistée - contrôlée intermittente à pression contrôlée", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "À 2 niveaux de pression", "cps_spec_requirement": "La ventilation à deux niveaux de pression positive Bi-Level", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Intelligente à aide adaptative", "cps_spec_requirement": "La ventilation intelligente à aide adaptative", "products_fulfills_requirement": ["Monnal TEO"]}, {"spec_name": "Pression positive à relachement de pression", "cps_spec_requirement": "La ventilation en pression positive à relâchements de pression", "products_fulfills_requirement": []}, {"spec_name": "Non invasive", "cps_spec_requirement": "La ventilation non invasive", "products_fulfills_requirement": ["SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "<PERSON><PERSON> (AI)", "cps_spec_requirement": "L’aide inspiratoire", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Spontanée en PEP", "cps_spec_requirement": "La ventilation spontanée ou PEEP", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}], "Paramètres": [{"spec_name": "Écran (pouces)", "cps_spec_requirement": "Ecran couleur d’au moins 7 pouces intégré au respirateur avec une résolution de 640×480 pixels au minimum.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Courbes temps réel (nombre)", "cps_spec_requirement": "Courbes en temps réel : pression, débit et volume", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "<PERSON><PERSON><PERSON> (nombre)", "cps_spec_requirement": "Boucles : volume/pression et Débit/volume", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Pinsp", "cps_spec_requirement": "L’aide inspiratoire : 0 à 60 cmH2O minimum", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "PEEP", "cps_spec_requirement": "Pression expiratoire (PEEP électronique intégré) : de 0 à 35 cm H2O minimum", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "VT", "cps_spec_requirement": "Volume courant : de 2 à 2000 ml", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Ftot", "cps_spec_requirement": "Fréquence respiratoire : de 1 à 80 cycles/min minimum", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Ti", "cps_spec_requirement": "Temps inspiratoire : 0,1 à 12 Sec minimum", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Rapport I:E", "cps_spec_requirement": "Rapport I/E modulable", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "FIO2", "cps_spec_requirement": "FIO2 de 21% à 100% minimum", "products_fulfills_requirement": ["SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Débit insp", "cps_spec_requirement": "Débit inspiratoire : 0 à 260 l/min minimum", "products_fulfills_requirement": ["EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}], "Alarmes: (oui/ message affiché)": [{"spec_name": "Alarmes code couleur ", "cps_spec_requirement": "Sonore et visuelle avec des indicateurs de la priorité (Haute, Moyenne et Basse)", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Défaut d'alimentation en gaz", "cps_spec_requirement": "Défaut d'alimentation en gaz.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Défaut d'alimentation électrique", "cps_spec_requirement": "Défaut d’alimentation électrique.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE NEO"]}, {"spec_name": "Batterie faible", "cps_spec_requirement": "Batterie faible (indicateur de niveau de charge sur l’écran)", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Concentration en CO2", "cps_spec_requirement": "Concentration en 02", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Volume minute bas", "cps_spec_requirement": "Volume minute bas.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Volume minute haut", "cps_spec_requirement": "Volume minute haut.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Pression inspiratoire basse", "cps_spec_requirement": "Pression inspiratoire basse", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Apnée", "cps_spec_requirement": "Apnée", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Pression élvée en permanence ", "cps_spec_requirement": "Pression haute.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Pression élevée en permanence ", "cps_spec_requirement": "Pression élevée en permanence.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Fréquence respiratoire haute", "cps_spec_requirement": "Fréquence respiratoire haute.", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "<PERSON><PERSON><PERSON> respiratoire basse", "cps_spec_requirement": "<PERSON><PERSON><PERSON> respiratoire basse", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE"]}], "Autres fonctions": [{"spec_name": "Nébuliseur", "cps_spec_requirement": "Nébulisation des médicaments", "products_fulfills_requirement": ["SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Oxygénothérapie", "cps_spec_requirement": "Mélangeur oxygène", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Auto test", "cps_spec_requirement": "Autotest du bon fonctionnement", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Compensation automatique de la résistance du tube", "cps_spec_requirement": "Compensation automatique de la sonde d'intubation", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Tendances (nombre simultané)", "cps_spec_requirement": "Le respirateur doit permettre l’enregistrement des tendances de 1 à 72 heures", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Autonomie batterie", "cps_spec_requirement": "Autonomie de la Batterie intégrée 240 min minimum", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}], "Accessoires : (oui /non/option)": [{"spec_name": "Humidifica<PERSON><PERSON> chauffant", "cps_spec_requirement": "Humidificateur", "products_fulfills_requirement": ["SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Chariot mobile d'origine", "cps_spec_requirement": "Chariot mobile ergonomique d’origine", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Bras articulé  supportant circuit patient", "cps_spec_requirement": "Bras articule support circuit patient", "products_fulfills_requirement": ["EVE", "EVE IN"]}, {"spec_name": "Nébuliseur", "cps_spec_requirement": "Accessoires complets pour nébulisation", "products_fulfills_requirement": ["SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Circuit patient en silicone autoclavable", "cps_spec_requirement": "2 circuits patient adulte et enfant réutilisable", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Filtre anti-bacterien", "cps_spec_requirement": "10 filtres anti bactériens", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "<PERSON> oxy<PERSON>", "cps_spec_requirement": "10 kits pour l'oxygénothérapie", "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Capteur débit autoclavable", "cps_spec_requirement": "2 Capteurs de débit adulte autoclavable", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Capteur débit autoclavable", "cps_spec_requirement": "2 Capteurs de débit enfant autoclavable", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}, {"spec_name": "Capteur CO2 (réutilisable ou usage unique)", "cps_spec_requirement": "01 Capteur CO2 réutilisable", "products_fulfills_requirement": ["Monnal TEO", "SOPHIE", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]}]}, "non_matched_criterias": [{"spec_name": "Ventilation en pression positive à relâchements de pression", "cps_spec_requirement": "La ventilation en pression positive à relâchements de pression"}, {"spec_name": "Ecran résolution", "cps_spec_requirement": "Ecran couleur d’au moins 7 pouces intégré au respirateur avec une résolution de 640×480 pixels au minimum."}]}, "matched_specs_count": 50, "non_matched_specs_count": 2, "output_format": "mini", "message": "Specification analysis completed using mini format. Only matched and non-matched criteria are returned."}