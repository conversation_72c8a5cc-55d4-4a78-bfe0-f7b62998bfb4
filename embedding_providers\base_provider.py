from abc import ABC, abstractmethod

class BaseEmbeddingProvider(ABC):
    """Base class for embedding providers"""

    @abstractmethod
    def get_embeddings(self, texts):
        """
        Get embeddings for a list of texts
        
        Args:
            texts (list): List of text strings to embed
            
        Returns:
            list: List of embedding vectors
        """
        pass
    
    @abstractmethod
    def compute_similarity(self, embedding1, embedding2):
        """
        Compute similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Similarity score (higher means more similar)
        """
        pass
