import json
import os
import re
import pickle

class EmbeddingSpecificationSelector:
    """
    Selector for specifications based on embeddings
    Uses embeddings to find the most relevant specifications for a query
    """

    def __init__(self, embedding_provider, similarity_threshold=0.7, embeddings_file="spec_embeddings.pkl"):
        """
        Initialize the selector

        Args:
            embedding_provider: The embedding provider to use
            similarity_threshold: Threshold for similarity score (higher means more selective)
            embeddings_file: File to store/load embeddings from
        """
        self.embedding_provider = embedding_provider
        self.similarity_threshold = similarity_threshold
        self.embeddings_file = embeddings_file
        self.specs_data = None
        self.specs_embeddings = {}
        self.specs_list = []
        self.group_mapping = {}

    def load_simplified_specs(self, file_path='simplified_specs.json'):
        """
        Load simplified specifications from JSON file

        Args:
            file_path (str): Path to the simplified specs JSON file

        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                self.specs_data = json.load(f)

            # Extract all specifications into a flat list
            self.specs_list = []
            self.group_mapping = {}

            for group_dict in self.specs_data.get('groups', []):
                for group_name, specs in group_dict.items():
                    for spec in specs:
                        self.specs_list.append(spec)
                        self.group_mapping[spec] = group_name

            return True

        except Exception as e:
            print(f"Error loading simplified specs: {e}")
            return False

    def generate_embeddings(self, force_regenerate=False):
        """
        Generate embeddings for all specifications

        Args:
            force_regenerate (bool): If True, regenerate embeddings even if they exist in file

        Returns:
            bool: True if embeddings were generated successfully, False otherwise
        """
        if not self.specs_list:
            print("No specifications loaded. Call load_simplified_specs first.")
            return False

        # Try to load embeddings from file if it exists
        if not force_regenerate and os.path.exists(self.embeddings_file):
            try:
                print(f"Loading embeddings from {self.embeddings_file}")
                with open(self.embeddings_file, 'rb') as f:
                    self.specs_embeddings = pickle.load(f)

                # Verify that all specs have embeddings
                if all(spec in self.specs_embeddings for spec in self.specs_list):
                    print(f"Successfully loaded embeddings for {len(self.specs_embeddings)} specifications")
                    return True
                else:
                    print("Some specifications are missing embeddings, regenerating...")
            except Exception as e:
                print(f"Error loading embeddings from file: {e}")

        try:
            # Generate embeddings for all specifications
            print("Generating embeddings for specifications...")
            embeddings = self.embedding_provider.get_embeddings(self.specs_list)

            # Store embeddings with their corresponding specifications
            self.specs_embeddings = {
                spec: embedding for spec, embedding in zip(self.specs_list, embeddings)
            }

            # Save embeddings to file
            try:
                with open(self.embeddings_file, 'wb') as f:
                    pickle.dump(self.specs_embeddings, f)
                print(f"Saved embeddings to {self.embeddings_file}")
            except Exception as e:
                print(f"Error saving embeddings to file: {e}")

            return True

        except Exception as e:
            print(f"Error generating embeddings: {e}")
            return False

    def _split_text_into_chunks(self, text, max_chunk_size=200, overlap=50):
        """
        Split text into overlapping chunks

        Args:
            text (str): Text to split
            max_chunk_size (int): Maximum size of each chunk in characters
            overlap (int): Overlap between chunks in characters

        Returns:
            list: List of text chunks
        """
        # Clean the text by removing excessive whitespace and special characters
        text = re.sub(r'\s+', ' ', text)
        text = text.replace('-', ' ').strip()

        # If text is shorter than max_chunk_size, return it as a single chunk
        if len(text) <= max_chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            # Determine end position for this chunk
            end = min(start + max_chunk_size, len(text))

            # If we're not at the end of the text, try to find a natural break point
            if end < len(text):
                # Look for the last sentence break or space within the chunk
                last_period = text.rfind('.', start, end)
                last_space = text.rfind(' ', start, end)

                # Prefer sentence breaks, but use space if no period is found
                if last_period > start + max_chunk_size // 2:
                    end = last_period + 1  # Include the period
                elif last_space > start + max_chunk_size // 3:
                    end = last_space

            # Add the chunk to our list
            chunks.append(text[start:end].strip())

            # Move the start position for the next chunk, accounting for overlap
            start = end - overlap if end < len(text) else len(text)

        return chunks

    def select_relevant_specs(self, query_text):
        """
        Select relevant specifications based on similarity to query

        Args:
            query_text (str): The query text to compare with specifications

        Returns:
            dict: Dictionary with selected specifications grouped by category
        """
        if not self.specs_embeddings:
            print("No embeddings available. Call generate_embeddings first.")
            return {}

        try:
            # Split the query text into chunks
            query_chunks = self._split_text_into_chunks(query_text)
            print(f"Split query into {len(query_chunks)} chunks")

            # Generate embeddings for all query chunks
            query_embeddings = self.embedding_provider.get_embeddings(query_chunks)

            # Track the best similarity score for each specification across all chunks
            best_similarities = {}

            # For each query chunk, calculate similarity with all specifications
            for chunk_idx, query_embedding in enumerate(query_embeddings):
                print(f"Processing chunk {chunk_idx+1}/{len(query_embeddings)}")

                for spec, spec_embedding in self.specs_embeddings.items():
                    similarity = self.embedding_provider.compute_similarity(query_embedding, spec_embedding)

                    # Keep track of the best similarity score for each specification
                    if spec not in best_similarities or similarity > best_similarities[spec]:
                        best_similarities[spec] = similarity

            # Select specifications above the threshold
            selected_specs = {
                spec: score for spec, score in best_similarities.items()
                if score >= self.similarity_threshold
            }

            print(f"Selected {len(selected_specs)} specifications with similarity >= {self.similarity_threshold}")

            # Group selected specifications by category
            grouped_specs = {}
            for spec in selected_specs:
                group = self.group_mapping.get(spec, "Other")
                if group not in grouped_specs:
                    grouped_specs[group] = []
                grouped_specs[group].append(spec)

            return grouped_specs

        except Exception as e:
            print(f"Error selecting relevant specifications: {e}")
            return {}
