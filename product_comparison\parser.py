class SpecificationParser:
    """Parser for product specifications"""
    
    def __init__(self, llm_provider):
        """
        Initialize the parser
        
        Args:
            llm_provider: The LLM provider to use for parsing
        """
        self.llm_provider = llm_provider
    
    def parse(self, specifications_text):
        """
        Parse product specifications text into structured format
        
        Args:
            specifications_text (str): Raw specifications text
            
        Returns:
            dict: Parsed specifications
        """
        return self.llm_provider.parse_specifications(specifications_text)
