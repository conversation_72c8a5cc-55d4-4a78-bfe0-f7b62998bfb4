#!/usr/bin/env python3
"""
Example demonstrating the new intelligent specification parsing functionality
This replaces the old regex/comma separation logic with AI-powered extraction
"""

import os
import json
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser

# Load environment variables
load_dotenv()

def demonstrate_intelligent_parsing():
    """Demonstrate the intelligent parsing capabilities"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot run demonstration")
        return
    
    print("=" * 80)
    print("INTELLIGENT SPECIFICATION PARSING DEMONSTRATION")
    print("Replacing regex/comma separation with AI-powered extraction")
    print("=" * 80)
    
    # Initialize the system
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()
    parser = IntelligentSpecificationParser(llm_provider)
    
    # Example customer input (mixed languages, various formats)
    customer_input = """
    Nous avons besoin d'un ventilateur avec les spécifications suivantes:
    
    Modes de ventilation:
    - Volume controlé obligatoire
    - Pression controlée si possible
    - Aide inspiratoire (AI) nécessaire
    - Mode non invasif souhaité
    
    Paramètres techniques:
    - PEEP réglable de 0 à 20 cmH2O
    - FIO2 de 21% à 100%
    - Volume courant (VT) ajustable
    - Fréquence respiratoire contrôlable
    
    Alarmes et sécurité:
    - Alarme de fuite indispensable
    - Alarme de déconnection requise
    - Défaut d'alimentation en gaz
    
    Fonctionnalités supplémentaires:
    - Affichage des courbes en temps réel
    - Auto-test automatique
    - Écran tactile couleur (non standard)
    - Connectivité WiFi (personnalisé)
    """
    
    print("CUSTOMER INPUT:")
    print("-" * 40)
    print(customer_input.strip())
    print()
    
    # Parse the specifications
    print("PROCESSING WITH AI...")
    print("-" * 40)
    result = parser.parse(customer_input)
    
    # Display detailed results
    print("PARSING RESULTS:")
    print("-" * 40)
    
    matched_criterias = result.get("matched_criterias", {})
    non_matched_criterias = result.get("non_matched_criterias", [])
    
    total_matched = sum(len(specs) for specs in matched_criterias.values())
    total_non_matched = len(non_matched_criterias)
    
    print(f"✅ MATCHED SPECIFICATIONS: {total_matched}")
    print(f"❌ NON-MATCHED SPECIFICATIONS: {total_non_matched}")
    print()
    
    # Show matched specifications by category
    if matched_criterias:
        print("MATCHED SPECIFICATIONS (will be used for product comparison):")
        print("-" * 60)
        for category, specs in matched_criterias.items():
            print(f"\n📁 {category}")
            for spec in specs:
                print(f"   ✓ {spec['spec_name']}: {spec['user_value']}")
                print(f"     └─ Original: \"{spec['user_input']}\"")
    
    # Show non-matched specifications
    if non_matched_criterias:
        print(f"\nNON-MATCHED SPECIFICATIONS (not in product database):")
        print("-" * 60)
        for spec in non_matched_criterias:
            print(f"   ❌ {spec['spec_name']}: {spec['user_value']}")
            print(f"      └─ Original: \"{spec['user_input']}\"")
    
    # Show what would be used for comparison
    comparison_specs = parser.get_matched_specs_for_comparison(result)
    print(f"\nSPECIFICATIONS READY FOR PRODUCT COMPARISON:")
    print("-" * 60)
    print(f"Total specifications for comparison: {len(comparison_specs)}")
    
    if comparison_specs:
        print("\nFlattened format for comparison engine:")
        for spec_name, value in comparison_specs.items():
            print(f"   {spec_name}: {value}")
    
    print("\n" + "=" * 80)
    print("BENEFITS OF INTELLIGENT PARSING:")
    print("=" * 80)
    print("✅ Handles multiple languages (French, English)")
    print("✅ Understands semantic variations and synonyms")
    print("✅ Maps to exact specification names from database")
    print("✅ Categorizes specifications by type")
    print("✅ Separates matched vs non-matched specifications")
    print("✅ Preserves original user input for reference")
    print("✅ No regex patterns or manual parsing rules needed")
    print("✅ Adapts to new specification formats automatically")
    print("=" * 80)

def show_comparison_with_old_method():
    """Show comparison between old and new parsing methods"""
    
    print("\n" + "=" * 80)
    print("COMPARISON: OLD vs NEW PARSING METHOD")
    print("=" * 80)
    
    sample_input = "Volume controlé: Oui, PEEP: 10 cmH2O, Custom alarm: Required"
    
    print(f"Sample Input: {sample_input}")
    print()
    
    print("OLD METHOD (Regex/Comma Separation):")
    print("-" * 40)
    print("❌ Split by commas: ['Volume controlé: Oui', 'PEEP: 10 cmH2O', 'Custom alarm: Required']")
    print("❌ Split by colon: {'Volume controlé': 'Oui', 'PEEP': '10 cmH2O', 'Custom alarm': 'Required'}")
    print("❌ No validation against available specifications")
    print("❌ No categorization")
    print("❌ No handling of semantic variations")
    print("❌ All specifications treated equally")
    print()
    
    print("NEW METHOD (AI-Powered):")
    print("-" * 40)
    print("✅ Intelligent extraction using simplified_specs.json")
    print("✅ Matched: 'Volume controlé' → Modes de ventilation")
    print("✅ Matched: 'PEEP' → Paramètres")
    print("✅ Non-matched: 'Custom alarm' → Not in database")
    print("✅ Proper categorization and validation")
    print("✅ Semantic understanding and mapping")
    print("✅ Only matched specs used for comparison")

if __name__ == "__main__":
    demonstrate_intelligent_parsing()
    show_comparison_with_old_method()
    
    print(f"\n{'='*80}")
    print("The intelligent parsing system is now ready!")
    print("It replaces all regex/comma separation logic with AI-powered extraction.")
    print(f"{'='*80}")
