#!/usr/bin/env python3
"""
Test the new intelligent parsing structure with products_specs.json
"""

import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_products_specs_loading():
    """Test loading products_specs.json"""
    print("=== Testing Products Specs Loading ===\n")
    
    try:
        with open('products_specs.json', 'r', encoding='utf-8') as f:
            products_specs = json.load(f)
        
        print(f"✅ Successfully loaded products_specs.json")
        
        total_specs = 0
        total_categories = 0
        
        for group in products_specs.get('groups', []):
            for category, spec_list in group.items():
                total_categories += 1
                total_specs += len(spec_list)
                print(f"   {category}: {len(spec_list)} specifications")
                
                # Show sample specification structure
                if spec_list:
                    sample_spec = spec_list[0]
                    print(f"     Sample: {sample_spec.get('Specifications', 'Unknown')}")
                    
                    # Count products
                    product_count = len([k for k in sample_spec.keys() if k != 'Specifications'])
                    print(f"     Products: {product_count}")
        
        print(f"\n   Total categories: {total_categories}")
        print(f"   Total specifications: {total_specs}")
        
        return products_specs
        
    except Exception as e:
        print(f"❌ Failed to load products_specs.json: {e}")
        return None

def test_parser_initialization():
    """Test parser initialization with new structure"""
    print("\n=== Testing Parser Initialization ===\n")
    
    try:
        from product_comparison.intelligent_parser import IntelligentSpecificationParser
        
        # Create a mock LLM provider
        class MockLLMProvider:
            def intelligent_parse_specifications(self, text, specs, chunk_size=10):
                return {
                    "matched_criterias": {
                        "Modes de ventilation:": [
                            {
                                "spec_name": "Volume controlé",
                                "cps_spec_requirement": "Must have volume control mode",
                                "user_input": "Volume controlé: Oui",
                                "products_evaluation": [
                                    {
                                        "product_name": "Monnal TEO",
                                        "product_value": "A/C VCV",
                                        "fulfills_requirement": True,
                                        "reasoning": "A/C VCV provides volume control functionality"
                                    },
                                    {
                                        "product_name": "SOPHIE",
                                        "product_value": "Non",
                                        "fulfills_requirement": False,
                                        "reasoning": "Does not support volume control mode"
                                    },
                                    {
                                        "product_name": "EVA",
                                        "product_value": "VC-VC",
                                        "fulfills_requirement": True,
                                        "reasoning": "VC-VC is volume control mode"
                                    }
                                ]
                            }
                        ]
                    },
                    "non_matched_criterias": [
                        {
                            "spec_name": "Custom feature",
                            "user_input": "Custom feature: Required"
                        }
                    ]
                }
        
        mock_provider = MockLLMProvider()
        
        # Test with different chunk sizes
        for chunk_size in [5, 10, -1]:
            print(f"Testing with chunk_size={chunk_size}")
            parser = IntelligentSpecificationParser(mock_provider, chunk_size=chunk_size)
            
            result = parser.parse("Volume controlé: Oui, Custom feature: Required")
            
            matched_count = sum(len(specs) for specs in result.get("matched_criterias", {}).values())
            non_matched_count = len(result.get("non_matched_criterias", []))
            
            print(f"  ✅ Matched: {matched_count}, Non-matched: {non_matched_count}")
            
            # Check structure
            matched = result.get("matched_criterias", {})
            if matched:
                for category, specs in matched.items():
                    for spec in specs:
                        products_evaluation = spec.get('products_evaluation', [])
                        fulfills_count = sum(1 for p in products_evaluation if p.get('fulfills_requirement', False))
                        print(f"    {spec['spec_name']}: {fulfills_count}/{len(products_evaluation)} products fulfill requirement")
        
        print("✅ Parser initialization successful")
        
    except Exception as e:
        print(f"❌ Parser initialization failed: {e}")

def test_expected_output_structure():
    """Test the expected output structure"""
    print("\n=== Testing Expected Output Structure ===\n")
    
    # Expected structure based on requirements
    expected_structure = {
        "matched_criterias": {
            "Modes de ventilation:": [
                {
                    "spec_name": "Volume controlé",
                    "cps_spec_requirement": "Must have volume control mode",
                    "user_input": "Volume controlé: Oui",
                    "products_evaluation": [
                        {
                            "product_name": "Monnal TEO",
                            "product_value": "A/C VCV",
                            "fulfills_requirement": True,
                            "reasoning": "A/C VCV provides volume control functionality"
                        },
                        {
                            "product_name": "SOPHIE",
                            "product_value": "Non",
                            "fulfills_requirement": False,
                            "reasoning": "Does not support volume control mode"
                        },
                        {
                            "product_name": "EVA",
                            "product_value": "VC-VC",
                            "fulfills_requirement": True,
                            "reasoning": "VC-VC is volume control mode"
                        }
                    ]
                }
            ],
            "Paramètres:": [
                {
                    "spec_name": "PEEP",
                    "cps_spec_requirement": "PEEP range 0-20 cmH2O",
                    "user_input": "PEEP: 0-20 cmH2O",
                    "products_evaluation": [
                        {
                            "product_name": "Monnal TEO",
                            "product_value": "0-50 cm H2O",
                            "fulfills_requirement": True,
                            "reasoning": "Range 0-50 cmH2O covers required 0-20 cmH2O"
                        },
                        {
                            "product_name": "SOPHIE",
                            "product_value": "0-30 mbar",
                            "fulfills_requirement": True,
                            "reasoning": "Range 0-30 mbar covers required range (converted units)"
                        }
                    ]
                }
            ]
        },
        "non_matched_criterias": [
            {
                "spec_name": "Custom alarm system",
                "user_input": "Custom alarm system: Required"
            }
        ]
    }
    
    print("✅ Expected structure:")
    print(f"   Matched categories: {len(expected_structure['matched_criterias'])}")
    
    for category, specs in expected_structure['matched_criterias'].items():
        print(f"     {category}: {len(specs)} specs")
        for spec in specs:
            products_evaluation = spec.get('products_evaluation', [])
            fulfills_count = sum(1 for p in products_evaluation if p.get('fulfills_requirement', False))
            print(f"       - {spec['spec_name']}: {fulfills_count}/{len(products_evaluation)} products fulfill requirement")
            print(f"         Requirement: {spec.get('cps_spec_requirement', 'N/A')}")
    
    print(f"   Non-matched specs: {len(expected_structure['non_matched_criterias'])}")
    for spec in expected_structure['non_matched_criterias']:
        print(f"     - {spec['spec_name']}")

def test_chunk_size_scenarios():
    """Test different chunk size scenarios"""
    print("\n=== Testing Chunk Size Scenarios ===\n")
    
    products_specs = test_products_specs_loading()
    if not products_specs:
        print("Cannot test chunk sizes without products_specs.json")
        return
    
    # Calculate total specifications
    total_specs = 0
    for group in products_specs.get('groups', []):
        for category, spec_list in group.items():
            total_specs += len(spec_list)
    
    print(f"Total specifications in database: {total_specs}")
    
    # Test different chunk sizes
    chunk_scenarios = [
        (5, f"Small chunks: {total_specs // 5 + 1} API calls"),
        (10, f"Medium chunks: {total_specs // 10 + 1} API calls"),
        (20, f"Large chunks: {total_specs // 20 + 1} API calls"),
        (-1, "Single call: 1 API call")
    ]
    
    for chunk_size, description in chunk_scenarios:
        print(f"  Chunk size {chunk_size}: {description}")

if __name__ == "__main__":
    test_products_specs_loading()
    test_parser_initialization()
    test_expected_output_structure()
    test_chunk_size_scenarios()
    
    print(f"\n{'='*60}")
    print("NEW STRUCTURE SUMMARY:")
    print("✅ Uses products_specs.json instead of simplified_specs.json")
    print("✅ Returns spec_name and cps_spec_requirement for matched criteria")
    print("✅ Evaluates each product against customer requirements")
    print("✅ Provides fulfills_requirement (True/False) and reasoning for each product")
    print("✅ Supports chunking (5, 10, 20, or -1 for all)")
    print("✅ No product comparison - only matched/non-matched criteria")
    print("✅ Processes groups sequentially")
    print(f"{'='*60}")
