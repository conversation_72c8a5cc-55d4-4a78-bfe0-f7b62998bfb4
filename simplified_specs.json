{"groups": [{"Modes de ventilation:": ["Volume controlé", "Pression controlée", "Assistée - controlée intermittente en volume", "Assistée - controlée intermittente en pression", "<PERSON><PERSON> (AI)", "Spontanée en PEP", "À 2 niveaux de pression", "Controlée à régulation de pression", "Pression positive à relachement de pression", "Intelligente à aide adaptative", "Non invasive", "Ventiliation intelligente", "Pression positive continue", "Sevrage rapide ( option)", "HFO"]}, {"Paramètres:": ["Pinsp", "PEEP", "Psup", "PAW", "Ppeak", "Pplat", "Pmean", "Ti", "Texp", "VT", "VT (vol controlé)", "VTI", "VTE", "VTE spn", "VT/IBW fuite", "VM ins (MVI)", "VM exp (MVE)", "VM fuite", "VME spn", "VCO2 exp", "ETCO2 (mmHg)", "Ftot", "Fcontrol", "Fspont", "HF-Freq", "HF-IE (pourcentage d'inspiration)", "Rapport I:E", "Trigger (l/min)", "Trigger externe (Arb)", "FIO2", "Auto-PEP", "Compléance pulmunaire", "Résistance pulmonaire", "Courbes temps réel (nombre)", "<PERSON><PERSON><PERSON> (nombre)", "Tendances (nombre simultané)", "Memorisation de l'affichage (ex: au moins 1000 évenements)", "Auto test", "Check-list automatique"]}, {"Alarmes: (oui/ message affiché)": ["<PERSON><PERSON><PERSON> fuite", "Alarme Déconnection", "Défaut d'alimentation en gaz", "Historique alarmes"]}]}