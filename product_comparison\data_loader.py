import json

def load_available_products(json_path):
    """
    Load available products from a JSON file

    Args:
        json_path (str): Path to the JSON file

    Returns:
        list: List of product dictionaries
    """
    try:
        with open(json_path, 'r', encoding='utf-8') as f:
            products = json.load(f)
        return products
    except Exception as e:
        print(f"Error loading available products: {e}")
        return []
