#!/usr/bin/env python3
"""
Test the mini and standard output formats
"""

import os
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_output_formats():
    """Test both mini and standard output formats"""
    print("=== Testing Mini and Standard Output Formats ===\n")
    
    try:
        from product_comparison.intelligent_parser import IntelligentSpecificationParser
        
        # Create a mock LLM provider that returns different formats
        class MockLLMProvider:
            def intelligent_parse_specifications(self, text, specs, chunk_size=10, include_reasoning=False, output_format="mini"):
                if output_format == "mini":
                    return {
                        "matched_criterias": {
                            "Modes de ventilation:": [
                                {
                                    "spec_name": "Volume controlé",
                                    "cps_spec_requirement": "La ventilation en volume contrôlée",
                                    "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO", "EVE NEO"]
                                },
                                {
                                    "spec_name": "Pression controlée",
                                    "cps_spec_requirement": "La ventilation assistée - contrôlée en pression",
                                    "products_fulfills_requirement": ["Monnal TEO", "EVA", "EVE", "EVE IN", "EVA NEO"]
                                }
                            ]
                        },
                        "non_matched_criterias": [
                            {
                                "spec_name": "Ventilation intelligente à aide adaptative",
                                "cps_spec_requirement": "La ventilation intelligente à aide adaptative"
                            }
                        ]
                    }
                else:  # standard format
                    base_result = {
                        "matched_criterias": {
                            "Modes de ventilation:": [
                                {
                                    "spec_name": "Volume controlé",
                                    "cps_spec_requirement": "La ventilation en volume contrôlée",
                                    "products_evaluation": [
                                        {
                                            "product_name": "Monnal TEO",
                                            "product_value": "A/C VCV",
                                            "fulfills_requirement": True
                                        },
                                        {
                                            "product_name": "SOPHIE",
                                            "product_value": "Non",
                                            "fulfills_requirement": False
                                        },
                                        {
                                            "product_name": "EVA",
                                            "product_value": "VC-VC",
                                            "fulfills_requirement": True
                                        },
                                        {
                                            "product_name": "EVE",
                                            "product_value": "VC-CMV, VC-S-IMV",
                                            "fulfills_requirement": True
                                        },
                                        {
                                            "product_name": "EVE IN",
                                            "product_value": "VC-VC, VC-VACI",
                                            "fulfills_requirement": True
                                        },
                                        {
                                            "product_name": "EVA NEO",
                                            "product_value": "VC-VC, VC-VACI",
                                            "fulfills_requirement": True
                                        },
                                        {
                                            "product_name": "EVE NEO",
                                            "product_value": "VC-VC",
                                            "fulfills_requirement": True
                                        }
                                    ]
                                }
                            ]
                        },
                        "non_matched_criterias": [
                            {
                                "spec_name": "Ventilation intelligente à aide adaptative",
                                "cps_spec_requirement": "La ventilation intelligente à aide adaptative"
                            }
                        ]
                    }
                    
                    # Add reasoning if requested
                    if include_reasoning:
                        for category, specs in base_result["matched_criterias"].items():
                            for spec in specs:
                                for product in spec["products_evaluation"]:
                                    if product["fulfills_requirement"]:
                                        product["reasoning"] = f"{product['product_value']} provides the required volume control functionality"
                                    else:
                                        product["reasoning"] = f"{product['product_value']} does not support volume control mode"
                    
                    return base_result
        
        mock_provider = MockLLMProvider()
        
        # Test mini format
        print("1. Testing MINI format:")
        print("-" * 40)
        parser_mini = IntelligentSpecificationParser(mock_provider, chunk_size=10, output_format="mini")
        result_mini = parser_mini.parse("La ventilation en volume contrôlée, La ventilation intelligente à aide adaptative")
        
        print("✅ Mini format structure:")
        matched = result_mini.get("matched_criterias", {})
        for category, specs in matched.items():
            print(f"   {category}: {len(specs)} specs")
            for spec in specs:
                fulfilling_products = spec.get('products_fulfills_requirement', [])
                print(f"     - {spec['spec_name']}: {len(fulfilling_products)} products fulfill")
                print(f"       Products: {', '.join(fulfilling_products[:3])}{'...' if len(fulfilling_products) > 3 else ''}")
        
        # Test standard format without reasoning
        print("\n2. Testing STANDARD format (no reasoning):")
        print("-" * 40)
        parser_standard = IntelligentSpecificationParser(mock_provider, chunk_size=10, include_reasoning=False, output_format="standard")
        result_standard = parser_standard.parse("La ventilation en volume contrôlée")
        
        print("✅ Standard format structure:")
        matched = result_standard.get("matched_criterias", {})
        for category, specs in matched.items():
            print(f"   {category}: {len(specs)} specs")
            for spec in specs:
                products_evaluation = spec.get('products_evaluation', [])
                fulfills_count = sum(1 for p in products_evaluation if p.get('fulfills_requirement', False))
                print(f"     - {spec['spec_name']}: {fulfills_count}/{len(products_evaluation)} products fulfill")
                
                # Check reasoning presence
                has_reasoning = any('reasoning' in p for p in products_evaluation)
                print(f"       Reasoning included: {has_reasoning}")
        
        # Test standard format with reasoning
        print("\n3. Testing STANDARD format (with reasoning):")
        print("-" * 40)
        parser_standard_reasoning = IntelligentSpecificationParser(mock_provider, chunk_size=10, include_reasoning=True, output_format="standard")
        result_standard_reasoning = parser_standard_reasoning.parse("La ventilation en volume contrôlée")
        
        print("✅ Standard format with reasoning:")
        matched = result_standard_reasoning.get("matched_criterias", {})
        for category, specs in matched.items():
            for spec in specs:
                products_evaluation = spec.get('products_evaluation', [])
                
                # Check reasoning presence
                has_reasoning = any('reasoning' in p for p in products_evaluation)
                print(f"     - {spec['spec_name']}: Reasoning included: {has_reasoning}")
                
                # Show sample reasoning
                for product in products_evaluation[:2]:
                    reasoning = product.get('reasoning', 'No reasoning')[:50] + "..." if len(product.get('reasoning', '')) > 50 else product.get('reasoning', 'No reasoning')
                    print(f"       {product['product_name']}: {reasoning}")
        
        print("\n✅ All format tests passed!")
        
    except Exception as e:
        print(f"❌ Format test failed: {e}")

def test_file_saving():
    """Test that analysis results are saved correctly"""
    print("\n=== Testing File Saving ===\n")
    
    # Check if analysis_results directory exists
    results_dir = "analysis_results"
    if os.path.exists(results_dir):
        files = os.listdir(results_dir)
        print(f"✅ Analysis results directory exists")
        print(f"   Files found: {len(files)}")
        
        # Show recent files
        for file in files[-3:]:  # Show last 3 files
            print(f"   - {file}")
    else:
        print("⚠ Analysis results directory not found")

if __name__ == "__main__":
    test_output_formats()
    test_file_saving()
    
    print(f"\n{'='*60}")
    print("FORMAT COMPARISON SUMMARY:")
    print("✅ MINI format: Optimized for speed, only lists fulfilling products")
    print("✅ STANDARD format: Complete evaluation with all products")
    print("✅ Reasoning: Optional in standard format, always considered internally")
    print("✅ File saving: Automatic saving to analysis_results folder")
    print("✅ Default settings: chunk_size=-1 (all items), output_format='mini'")
    print(f"{'='*60}")
