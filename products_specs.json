{"groups": [{"Modes de ventilation:": [{"Specifications": "Volume controlé", "Monnal TEO": "A/C VCV", "SOPHIE": "Non ", "EVA": "VC-VC", "EVE": "VC-CMV, VC-S-IMV", "EVE IN": "VC-VC, VC-VACI", "EVA NEO": "VC-VC, VC-VACI", "EVE NEO": " VC-VC  "}, {"Specifications": "Pression controlée", "Monnal TEO": "A/C PC", "SOPHIE": "VCI", "EVA": "VC-VC", "EVE": "PC-VC, nPC-VC, PC-VAC, nPC-VAC", "EVE IN": "PC-VC, PC-VACI, PC-VAC, PC-VAC+", "EVA NEO": "PC-VC, PC-VACI, PC-VAC", "EVE NEO": "PC-VC"}, {"Specifications": "Assistée - controlée intermittente en volume", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "VC-VACI ", "EVE": "VC-S-IMV", "EVE IN": "VC-VACI ", "EVA NEO": "VC-VACI", "EVE NEO": "VC-VACI "}, {"Specifications": "Assistée - controlée intermittente en pression", "Monnal TEO": "PSIMV", "SOPHIE": "VACI", "EVA": "PC-VACI", "EVE": "PC-VACI, nPC-VACI", "EVE IN": "PC-VACI", "EVA NEO": "PC-VACI", "EVE NEO": "PC-VACI"}, {"Specifications": "<PERSON><PERSON> (AI)", "Monnal TEO": "PSV", "SOPHIE": "PPSV% p33", "EVA": "AI-PSV ", "EVE": "AI-PSV", "EVE IN": "Oui (VS-PEP+AI-PSV)", "EVA NEO": "AI-PSV (disponible avec PC-VACI, VC-VACI, VSPEP et DUOPAP)", "EVE NEO": "OUI (AI-PSV)"}, {"Specifications": "Spontanée en PEP", "Monnal TEO": "PSV, PS-Pro", "SOPHIE": "VS-PEP, nVS-PEP", "EVA": "VS-PEP, nVS-PEP", "EVE": "VSPEP, NI-VSPEP", "EVE IN": "VS-PEP, nVS-PEP", "EVA NEO": "VS-PEP, nVS-PEP", "EVE NEO": "VS-PEP, nVS-PEP"}, {"Specifications": "À 2 niveaux de pression", "Monnal TEO": "DUO-Levels ", "SOPHIE": "Non ", "EVA": "DUOPAP, nDUOPAP", "EVE": "DUOPAP, nDUOPAP", "EVE IN": "DUOPAP", "EVA NEO": "DUOPAP, nDUOPAP", "EVE NEO": "DUOPAP"}, {"Specifications": "Controlée à régulation de pression", "Monnal TEO": "PRCV", "SOPHIE": "VCI : Ventilation contrôlée intermittente -p174", "EVA": "VPC-VG", "EVE": "PC-VAC+, nPC-VAC+", "EVE IN": "VPC-VG", "EVA NEO": "VPC-VG", "EVE NEO": "VPC-VG"}, {"Specifications": "Pression positive à relachement de pression", "Monnal TEO": "non", "SOPHIE": "Non ", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Intelligente à aide adaptative", "Monnal TEO": "PS-PRO", "SOPHIE": "Non ", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Non invasive", "Monnal TEO": "****", "SOPHIE": "VPPNI, nVS-PEP", "EVA": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "EVE": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "EVE IN": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nDUOPAP, nVS-PEP", "EVA NEO": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "EVE NEO": " nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nDUOPAP, nVS-PEP)"}, {"Specifications": "Ventiliation intelligente", "Monnal TEO": "****", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Pression positive continue", "Monnal TEO": "CPAP", "SOPHIE": "VS-PEP, nVS-PEP", "EVA": "VS-PEP, nVS-PEP", "EVE": "VS-PEP, nVS-PEP", "EVE IN": "VS-PEP, nVS-PEP", "EVA NEO": "VS-PEP, nVS-PEP", "EVE NEO": "VS-PEP, nVS-PEP"}, {"Specifications": "Sevrage rapide ( option)", "Monnal TEO": "APRV, SIMV, PSV", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "HFO", "Monnal TEO": "Non", "SOPHIE": "<PERSON><PERSON> (HFO, sHFO)", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}]}, {"Paramètres:": [{"Specifications": "Pinsp", "Monnal TEO": "2-99 cmH2O", "SOPHIE": "Pmax: 5 à 60 mbar", "EVA": "1-55 mbar(neo/ped), 1-95 (adulte)", "EVE": "1-55 mbar(neo/ped), 1-95 (adulte)", "EVE IN": "1-55 mbar(neo/ped), 1-95 (adulte)", "EVA NEO": "1-55 mbar(neo/ped), 1-95 (adulte)", "EVE NEO": "1-55 mbar(neo/ped), 1-95 (adulte)"}, {"Specifications": "PEEP", "Monnal TEO": "0-50 cm H2O", "SOPHIE": "0-30 mbar", "EVA": "0-35 mbar", "EVE": "0-35 mbar", "EVE IN": "0-35 (mode neo), 0-35 (ped/adulte)", "EVA NEO": "0-35 mbar", "EVE NEO": "0-35 (mode neo), 0-35 (ped/adulte)"}, {"Specifications": "Psup", "Monnal TEO": " 2-40 cmH2O", "SOPHIE": "PPSV%: 0 à 100%", "EVA": "1-55 mbar (tous les modes)", "EVE": "1-55 mbar (tous les modes)", "EVE IN": "1-55 mbar (tous les modes)", "EVA NEO": "1-55 mbar (tous les modes)", "EVE NEO": "1-55 mbar (tous les modes)"}, {"Specifications": "PAW", "Monnal TEO": "*****", "SOPHIE": "*****", "EVA": "0 à 59/11 à 60 (ped)           0 à 99/11 à 100 (adulte)", "EVE": "1 à 59/11 à 60 (ped)           0 à 99/11 à 100 (adulte)", "EVE IN": "0 à 59/11 à 60 (ped)           0 à 99/11 à 100 (adulte)", "EVA NEO": "0 à 59/11 à 60 (ped)           0 à 99/11 à 100 (adulte)", "EVE NEO": "0 à 59/11 à 60 (ped)           0 à 99/11 à 100 (adulte)"}, {"Specifications": "Ppeak", "Monnal TEO": "0-100 l/min", "SOPHIE": "de -20 à 99 mbar", "EVA": "de -20 à 99 mbar", "EVE": "de -20 à 99 mbar", "EVE IN": "de -20 à 99 mbar", "EVA NEO": "de -20 à 99 mbar", "EVE NEO": "de -20 à 99 mbar"}, {"Specifications": "Pplat", "Monnal TEO": "2-60 cm H20", "SOPHIE": "*****", "EVA": "de -20 à 99 mbar", "EVE": "de -20 à 99 mbar", "EVE IN": "de -20 à 99 mbar", "EVA NEO": "de -20 à 99 mbar", "EVE NEO": "de -20 à 99 mbar"}, {"Specifications": "Pmean", "Monnal TEO": "0-60 cm H20", "SOPHIE": "de 0 à30 mbar", "EVA": "de -20 à 99 mbar", "EVE": "de -20 à 99 mbar", "EVE IN": "de -20 à 99 mbar", "EVA NEO": "de -20 à 99 mbar", "EVE NEO": "de -20 à 99 mbar"}, {"Specifications": "Ti", "Monnal TEO": "0,2-10s", "SOPHIE": "0,1-2 s", "EVA": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVE": "0,15 à30 sec. (NEO-Mode),\n0,2 à 30 sec. (Ped./Adult-Mode)", "EVE IN": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVA NEO": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVE NEO": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)"}, {"Specifications": "Texp", "Monnal TEO": "****", "SOPHIE": "0,1-60 s", "EVA": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVE": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVE IN": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVA NEO": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "EVE NEO": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)"}, {"Specifications": "VT", "Monnal TEO": "adulte: 100-2000ml \nenfant : 50-500ml \nnourrissons : 20-75     ", "SOPHIE": "(VtTar) 2-150 ml", "EVA": "2-2000 ml", "EVE": "2-2000 ml", "EVE IN": "2-2000 ml", "EVA NEO": "2-2000 ml", "EVE NEO": "2-2000 ml"}, {"Specifications": "VT (vol controlé) ", "Monnal TEO": null, "SOPHIE": "****", "EVA": "50-2000 ml", "EVE": "50-2000 ml", "EVE IN": "50-2000 ml", "EVA NEO": "50-2000 ml", "EVE NEO": "50-2000 ml"}, {"Specifications": "VTI", "Monnal TEO": "20-3000 mL", "SOPHIE": "0-999 L", "EVA": "****", "EVE": "0- 3000 mL", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "VTE", "Monnal TEO": "20-3000 mL", "SOPHIE": "0-999 L", "EVA": "0- 3000 mL", "EVE": "0- 3000 mL", "EVE IN": "0- 3000 mL", "EVA NEO": "0- 3000 mL", "EVE NEO": "0- 3000 mL"}, {"Specifications": "VTE spn", "Monnal TEO": "****", "SOPHIE": "*****", "EVA": "0- 3000 mL", "EVE": "0- 3000 mL", "EVE IN": "0- 3000 mL", "EVA NEO": "0- 3000 mL", "EVE NEO": "0- 3000 mL"}, {"Specifications": "VT/IBW fuite", "Monnal TEO": "1-20 mL/kg", "SOPHIE": "*****", "EVA": "****", "EVE": "****", "EVE IN": "0- 1000 mL", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "VM  ins (MVI)", "Monnal TEO": "******", "SOPHIE": "*****", "EVA": "****", "EVE": "0-999 L/min", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "VM exp (MVE)", "Monnal TEO": "0-40 l/min", "SOPHIE": "0-999 L/min", "EVA": "0-999 L/min", "EVE": "0-999 L/min", "EVE IN": "0-999 L/min", "EVA NEO": "0-999 L/min", "EVE NEO": "0-999 L/min"}, {"Specifications": "VM fuite", "Monnal TEO": "****", "SOPHIE": "****", "EVA": "****", "EVE": "0-5000 mL", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "VME spn", "Monnal TEO": "0-40 l/min", "SOPHIE": "****", "EVA": " 0-999 l/min", "EVE": " 0-999 l/min", "EVE IN": "0-999 L/min", "EVA NEO": " 0-999 l/min", "EVE NEO": "0-999 L/min"}, {"Specifications": "VCO2 exp", "Monnal TEO": "****", "SOPHIE": "****", "EVA": "****", "EVE": "****", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "ETCO2  (mmHg)", "Monnal TEO": "0-120 mmHg", "SOPHIE": "****", "EVA": "0 à 12", "EVE": "0 à 12", "EVE IN": "0 à 12", "EVA NEO": "0 à 12", "EVE NEO": "1 à 12"}, {"Specifications": "Ftot", "Monnal TEO": "****", "SOPHIE": "1-300/min", "EVA": "1-200 (mode neo), 1-150 (ped/adulte)", "EVE": "1-200 (mode neo), 1-150 (ped/adulte)", "EVE IN": "1-200 (mode neo), 1-150 (ped/adulte)", "EVA NEO": "1-200 (mode neo), 1-150 (ped/adulte)", "EVE NEO": "1-200 (mode neo), 1-150 (ped/adulte)"}, {"Specifications": "Fcontrol", "Monnal TEO": "****", "SOPHIE": "****", "EVA": "****", "EVE": "****", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "Fspont", "Monnal TEO": "****", "SOPHIE": "****", "EVA": "0-300 l/min", "EVE": "0 -300 l/min", "EVE IN": "0 -300 l/min", "EVA NEO": "0 -300 l/min", "EVE NEO": "0 -300 l/min"}, {"Specifications": "HF-Freq", "Monnal TEO": "*****", "SOPHIE": "5 à 15 Hz", "EVA": "*****", "EVE": "*****", "EVE IN": "*****", "EVA NEO": "*****", "EVE NEO": "*****"}, {"Specifications": "HF-IE (pourcentage d'inspiration)", "Monnal TEO": "*****", "SOPHIE": "33 à 50 %", "EVA": "*****", "EVE": "*****", "EVE IN": "*****", "EVA NEO": "*****", "EVE NEO": "*****"}, {"Specifications": "HF-AM (Amplitude de la pression\nd'oscillation pour HFO)", "Monnal TEO": "*****", "SOPHIE": "5 à 100 %", "EVA": "*****", "EVE": "*****", "EVE IN": "*****", "EVA NEO": "*****", "EVE NEO": "*****"}, {"Specifications": "Débit insp", "Monnal TEO": "0-150 L/min", "SOPHIE": "*****", "EVA": "de -200 à 200 l/min ", "EVE": "de -200 à 200 l/min ", "EVE IN": "de -200 à 200 l/min ", "EVA NEO": "de -200 à 200 l/min ", "EVE NEO": "de -200 à 200 l/min "}, {"Specifications": "Débit exp", "Monnal TEO": "0-150 L/min", "SOPHIE": "*****", "EVA": "de -200 à 200 l/min ", "EVE": "de -200 à 200 l/min ", "EVE IN": "de -200 à 200 l/min ", "EVA NEO": "de -200 à 200 l/min ", "EVE NEO": "de -200 à 200 l/min "}, {"Specifications": "Rapport I:E", "Monnal TEO": "1:0.2-1:19", "SOPHIE": null, "EVA": "1:199 ... 199:1 (Mode NEO) 1:150 ... 150:1 (Mode péd./adulte)", "EVE": "1:150 ... 150:1 (Adult-Mode) 1:200 ... 200:1 (NEO/Ped.-Mode)", "EVE IN": "1:150 ... 150:1 (Adult-Mode) 1:200 ... 200:1 (NEO/Ped.-Mode)", "EVA NEO": "1:199/199:1 (Néo)", "EVE NEO": "1:150 ... 150:1 (Adult/Ped) 1:200 ... 200:1 (NEO)"}, {"Specifications": "Trigger (l/min)", "Monnal TEO": "entre 0,2-15 l/min (off)", "SOPHIE": "0,2 à 2,9 l/min", "EVA": "0,2 à 15 l/min", "EVE": "0,2 à 15 l/min", "EVE IN": "0,2 à 15 l/min", "EVA NEO": "0,2 à 15 l/min", "EVE NEO": "0,2 à 15 l/min"}, {"Specifications": "Trigger externe (Arb)", "Monnal TEO": "5-90 l/min ", "SOPHIE": "0,2-15 Arb (avec licence)", "EVA": "Oui (avec licence)", "EVE": "Oui (avec licence)", "EVE IN": "Oui (avec licence)", "EVA NEO": "Oui (avec licence)", "EVE NEO": "Oui (avec licence)"}, {"Specifications": "FIO2", "Monnal TEO": "30-60%", "SOPHIE": "21-100 %", "EVA": "21-100 %", "EVE": "21-100 %", "EVE IN": "21-100 %", "EVA NEO": "21-100 %", "EVE NEO": "21-100 %"}, {"Specifications": "Auto-PEP", "Monnal TEO": "****", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Cellule O2 (paramagnétique/ chimique)", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Capteur d'oxygène électrochimique de type M-11 ", "EVA": "Capteur d'oxy<PERSON><PERSON>lectrochi<PERSON> (MLF-16 ou M-16)", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "Capteur d'oxy<PERSON><PERSON>lectrochi<PERSON> (MLF-16 ou M-16)", "EVE NEO": "O<PERSON> "}, {"Specifications": "Capteur CO2 (capnographe)", "Monnal TEO": "****", "SOPHIE": "Optionnel", "EVA": "Optionnel (MASIMO IRMA CO2)", "EVE": "Optionnel (Masimo IRMA CO2, Masimo ISA)", "EVE IN": "Optionnel (Masimo IRMA CO2, Masimo ISA)", "EVA NEO": "Optionnel (Masimo IRMA CO2, Masimo ISA)", "EVE NEO": "Optionnel (Masimo IRMA CO2, Masimo ISA)"}, {"Specifications": "Capteur SPO2", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Optionnel", "EVA": "Optionnel (Masimo Rainbow SET)", "EVE": "Optionnel (Masimo Rainbow SET)", "EVE IN": "Optionnel (Masimo Rainbow SET)", "EVA NEO": "Optionnel (Masimo Rainbow SET)", "EVE NEO": "Optionnel (Masimo Rainbow SET)"}, {"Specifications": "Préoxygénation temps", "Monnal TEO": "****", "SOPHIE": "0- 420s", "EVA": "10 à 180 s", "EVE": "10 à 180 s", "EVE IN": "10 à 180 s", "EVA NEO": "10 à 180 s", "EVE NEO": "10 à 180 s"}, {"Specifications": "Préoxygénation débit", "Monnal TEO": "****", "SOPHIE": "21 à 100 %", "EVA": "21 à 100 % ", "EVE": "21 à 100 %", "EVE IN": "21 à 100 %", "EVA NEO": "21 à 100 %", "EVE NEO": "21 à 100 %"}, {"Specifications": "CPAP", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON>", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "Oui (VS-PEP)", "EVA NEO": "Non", "EVE NEO": "Oui (VS-PEP)"}, {"Specifications": "DUOPAP", "Monnal TEO": "O<PERSON>", "SOPHIE": "Non", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON>"}, {"Specifications": "Phaute (Pinsp DUOPAP) ", "Monnal TEO": "O<PERSON>", "SOPHIE": "Non", "EVA": "1 à 55 mbar (neo/ped), 1 à 95 mbar (adulte)", "EVE": "1 à 55 mbar (neo/ped), 1 à 95 mbar (adulte)", "EVE IN": "1 à 55 mbar (neo/ped), 1 à 95 mbar (adulte)", "EVA NEO": "1 à 55 mbar (neo/ped), 1 à 95 mbar (adulte)", "EVE NEO": "1 à 55 mbar (neo/ped), 1 à 95 mbar (adulte)"}, {"Specifications": "Oxygénothérapie", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Non ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "<PERSON><PERSON> (HFO2)", "EVA NEO": "<PERSON><PERSON> (High-Flow)", "EVE NEO": "O<PERSON> "}, {"Specifications": "Débit oxygénothérapie", "Monnal TEO": null, "SOPHIE": "Non", "EVA": "2 à 60 l/min", "EVE": "env. 5 l/min.", "EVE IN": "2 à 60 l/min", "EVA NEO": "2 à 60 l/min", "EVE NEO": "2 à 60 l/min"}, {"Specifications": "Lavage de l'espace mort rhinopharyngien", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "<PERSON>on (endotrachéal)", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Pression ballon", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Contrôle automatique/ temps réel pression du ballon ", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Possibilité de dégonflage complet du ballon", "Monnal TEO": "Non", "SOPHIE": "Non", "EVA": "Non", "EVE": "Non", "EVE IN": "Non", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Compensation automatique de la résistance du tube", "Monnal TEO": "0-100%", "SOPHIE": "Non", "EVA": "0 à 100 %", "EVE": "0 à 100 %", "EVE IN": "0 à 100 %", "EVA NEO": "1 à 100 %", "EVE NEO": "0 à 100 %"}, {"Specifications": "Humidificateur intelligent (type: (ex :chau<PERSON><PERSON>))", "Monnal TEO": "****", "SOPHIE": "Humidificateur chauffant intégré ", "EVA": "option, chauffé ", "EVE": "option, chauffé", "EVE IN": "option, chauffé", "EVA NEO": "option, chauffé", "EVE NEO": "option, chauffé"}, {"Specifications": "Nébuliseur", "Monnal TEO": "Non", "SOPHIE": "Nébuliseur pneumatique de médicaments", "EVA": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f\n", "EVE": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f\n", "EVE IN": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f\n", "EVA NEO": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f\n", "EVE NEO": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f\n"}, {"Specifications": "Compléance pulmunaire", "Monnal TEO": "1-100 ml/cmH2O", "SOPHIE": "0 à 999 ml/mbar", "EVA": "0 à 650 ml/mbar", "EVE": "0 à 1000 ml / mbar", "EVE IN": "1 à 650 ml/mbar", "EVA NEO": "1 à 650 ml/mbar", "EVE NEO": "0 à 650 ml/mbar"}, {"Specifications": "Résistance pulmonaire", "Monnal TEO": "5-200 ml/cmH2O", "SOPHIE": "0 à 999 mbar/l/s", "EVA": "0 à 1000 mbar/l/s", "EVE": "0 à 1000 mbar / L/s", "EVE IN": "1 à 1000 mbar/l/s", "EVA NEO": "1 à 1000 mbar/l/s", "EVE NEO": "0 à 1000 mbar/l/s"}, {"Specifications": "Courbes temps réel (nombre)", "Monnal TEO": 4, "SOPHIE": 5, "EVA": 5, "EVE": 5, "EVE IN": 5, "EVA NEO": 5, "EVE NEO": 5}, {"Specifications": "<PERSON><PERSON><PERSON> (nombre)", "Monnal TEO": 3, "SOPHIE": 3, "EVA": 3, "EVE": 3, "EVE IN": 3, "EVA NEO": 3, "EVE NEO": 3}, {"Specifications": "Tendances (nombre simultané)", "Monnal TEO": "Jusqu'à 4 courbes simultanement", "SOPHIE": "Jusqu'à 2 courbes simultanement", "EVA": "Jusqu'à 2 courbes simultanement", "EVE": "Jusqu'à 2 courbes simultanement", "EVE IN": "Jusqu'à 2 courbes simultanement", "EVA NEO": "Jusqu'à 2 courbes simultanement", "EVE NEO": "Jusqu'à 2 courbes simultanement"}, {"Specifications": "Memorisation de l'affichage (ex:  au moins 1000 évenements)", "Monnal TEO": "1 000 événements (les plus anciens sont écrasés lorsque la mémoire est pleine)", "SOPHIE": "Historique des alarmes conservé jusqu'à l'arrêt complet de l'appareil.", "EVA": "1100 entrées d'événements ", "EVE": "1101 entrées d'événements ", "EVE IN": "1100 entrées d'événements ", "EVA NEO": "1100 entrées d'événements ", "EVE NEO": "1100 entrées d'événements "}, {"Specifications": "Autonomie batterie", "Monnal TEO": "5 heures", "SOPHIE": "Au moins 80 min", "EVA": "Max. 4 heures", "EVE": "Max. 3h  (2,1 Ah)              Max 4h (3,12 Ah) ", "EVE IN": "Max. 3h  (2,1 Ah)              Max 4h (3,12 Ah) ", "EVA NEO": "Max. 4 heures", "EVE NEO": "Max. 3h  (2,1 Ah)              Max 4h (3,12 Ah) "}, {"Specifications": "Écran (pouces)", "Monnal TEO": "15 pouces", "SOPHIE": 12.1, "EVA": 12.2, "EVE": "8,4\"", "EVE IN": "8,4\"", "EVA NEO": 12.2, "EVE NEO": "8,4\""}, {"Specifications": "Écran (type: Tactile/TFT/LCD)", "Monnal TEO": "tactile  ", "SOPHIE": "Tactile TFT", "EVA": "Tactile TFT", "EVE": "Tactile TFT ", "EVE IN": "Tactile TFT ", "EVA NEO": "Tactile TFT", "EVE NEO": "Tactile TFT "}, {"Specifications": "Écran: résolution", "Monnal TEO": null, "SOPHIE": "****", "EVA": "****", "EVE": "****", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "Alimentation O2", "Monnal TEO": "280 à 600 kPa(haute pression)150 kPa(basse pression)", "SOPHIE": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVA": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVE": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVE IN": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVA NEO": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVE NEO": "280-600 kPa (haute pression), 0-150 kPa (basse pression)"}, {"Specifications": "Alimentation AIR", "Monnal TEO": "Non (turbine intégrée) et Prise d'air ambiante d'urgence", "SOPHIE": "280-600 kPa (haute pression), 0-150 kPa (basse pression)", "EVA": "Non (turbine intégrée)", "EVE": "Non (turbine intégrée)", "EVE IN": "Non (turbine intégrée)", "EVA NEO": "Non (turbine intégrée)", "EVE NEO": "Non (turbine intégrée)"}, {"Specifications": "Detection Apnée", "Monnal TEO": "O<PERSON>", "SOPHIE": "Oui (2 à16 s)", "EVA": "Oui (4 à 60 s)", "EVE": "4 à 60 s", "EVE IN": "Oui (4 à 60 s)", "EVA NEO": "Oui (4 à 60 s)", "EVE NEO": "Oui (1 à 60 s)"}, {"Specifications": "Auto test", "Monnal TEO": "O<PERSON>", "SOPHIE": "O<PERSON>", "EVA": "Suite à la mise sous tension, l'appareil EVA exécute un autotest automatique", "EVE": "O<PERSON>", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON>", "EVE NEO": "O<PERSON>"}, {"Specifications": "Check-list automatique", "Monnal TEO": "autotest ", "SOPHIE": "O<PERSON>", "EVA": "O<PERSON>", "EVE": "O<PERSON>", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON>", "EVE NEO": "O<PERSON> "}, {"Specifications": "Ports de communication (USB, appel infirmier….)", "Monnal TEO": "O<PERSON>", "SOPHIE": "USB\nEthernet\nInterface COM\nAppel infirmiers", "EVA": "USB\nEthernet\nInterface COM\nAppel infirmiers", "EVE": "USB\nEthernet\nInterface COM\nAppel infirmiers", "EVE IN": "USB\nEthernet\nInterface COM\nAppel infirmiers", "EVA NEO": "USB\nEthernet\nInterface COM\nAppel infirmiers", "EVE NEO": "USB\nEthernet\nInterface COM\nAppel infirmiers"}]}, {"Alarmes: (oui/ message affiché)": [{"Specifications": "<PERSON><PERSON><PERSON> fuite", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON>  ", "EVE IN": "O<PERSON>  ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Alarme Déconnection", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Défaut d'alimentation en gaz", "Monnal TEO": "O<PERSON> ", "SOPHIE": "\"Entrée air\", \"Entrée oxygène\", \"Arrivée gaz\"", "EVA": "'O2 pression basse''", "EVE": "'O2 pression basse''", "EVE IN": "'O2 pression basse''", "EVA NEO": "'O2 pression basse''", "EVE NEO": "'O2 pression basse''"}, {"Specifications": "Défaut d'alimentation électrique", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON>", "EVA": "O<PERSON>", "EVE": "O<PERSON>", "EVE IN": "*****", "EVA NEO": "*****", "EVE NEO": "O<PERSON> "}, {"Specifications": "Batterie faible", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON>", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Concentration en CO2", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Non ", "EVA": "EtCO2 haute/basse", "EVE": "EtCO2 haute/basse", "EVE IN": "EtCO2 haute/basse", "EVA NEO": "EtCO2 haute/basse", "EVE NEO": "O<PERSON> "}, {"Specifications": "Volume minute bas", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Volume minute haut", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Pression inspiratoire basse", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Apnée", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Pression élvée en permanence ", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "'Pression ventilation haute''", "EVE": "'Pression ventilation haute''", "EVE IN": "'Pression ventilation haute''", "EVA NEO": "'Pression ventilation haute''", "EVE NEO": "'Pression ventilation haute''"}, {"Specifications": "Fréquence respiratoire haute", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "<PERSON><PERSON><PERSON> respiratoire basse", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "****", "EVE": "****", "EVE IN": "****", "EVA NEO": "****", "EVE NEO": "****"}, {"Specifications": "Alarmes audio-visuelles", "Monnal TEO": "O<PERSON> ", "SOPHIE": "<PERSON><PERSON> (47,5-70 dB)", "EVA": "<PERSON><PERSON> (46-56 dB)", "EVE": "<PERSON><PERSON> (72-80 dB)", "EVE IN": "<PERSON><PERSON> (72-80 dB)", "EVA NEO": "<PERSON><PERSON> (72-80 dB)", "EVE NEO": "<PERSON><PERSON> (58-68 dB)"}, {"Specifications": "Alarmes code couleur ", "Monnal TEO": "O<PERSON> ", "SOPHIE": " <PERSON> (HP), <PERSON><PERSON><PERSON> (MP), cyan (faible)", "EVA": " <PERSON> (HP), <PERSON><PERSON><PERSON> (MP), <PERSON><PERSON><PERSON> (indications)", "EVE": "Alarmes priorité haute (HP) et moyenne (MP)", "EVE IN": "Alarmes priorité haute (HP) et moyenne (MP)", "EVA NEO": " <PERSON> (HP), <PERSON><PERSON><PERSON> (MP), <PERSON><PERSON><PERSON> (indications)", "EVE NEO": "Alarmes priorité haute (HP) et moyenne (MP)"}, {"Specifications": "Historique alarmes", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Liste complète des alarmes avec date/heure, état (active/inactive) et priorité", "EVA": " 7 dernières alarmes affichées", "EVE": " 8 dernières alarmes par ordre chronologique", "EVE IN": " 7 dernières alarmes par ordre chronologique", "EVA NEO": " 7 dernières alarmes affichées", "EVE NEO": " 7 dernières alarmes par ordre chronologique"}]}, {"Accessoires : (oui /non/option)": [{"Specifications": "Chariot mobile d'origine", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Roues avec freins", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "<PERSON><PERSON>, 4 roulettes avec freins ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": " <PERSON><PERSON>, 4 roulettes avec freins", "EVE NEO": "O<PERSON>"}, {"Specifications": "Bras articulé  supportant circuit patient", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Non", "EVA": "Non", "EVE": "<PERSON><PERSON> (option)", "EVE IN": "<PERSON><PERSON> (option)", "EVA NEO": "Non", "EVE NEO": "Non"}, {"Specifications": "Capteur débit autoclavable", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON>", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON>"}, {"Specifications": "Circuit patient en silicone autoclavable", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON>", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON>"}, {"Specifications": "Filtre anti-bacterien", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON>", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON>"}, {"Specifications": "<PERSON> oxy<PERSON>", "Monnal TEO": "O<PERSON> ", "SOPHIE": "Non", "EVA": "O<PERSON> ", "EVE": "O<PERSON>", "EVE IN": "O<PERSON>", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON>"}, {"Specifications": "Capteur CO2 (réutilisable ou usage unique)", "Monnal TEO": "Option ", "SOPHIE": "Option", "EVA": "Optionnel, réutilisable ou usage unique", "EVE": "Optionnel, réutilisable ou usage unique", "EVE IN": "Optionnel, réutilisable ou usage unique", "EVA NEO": "Optionnel, réutilisable ou usage unique", "EVE NEO": "Optionnel, réutilisable (Masimo)"}, {"Specifications": "Capteur SPO2 (réutilisable ou usage unique)", "Monnal TEO": "Optionnel, réutilisable", "SOPHIE": "Option", "EVA": "Optionnel, réutilisable (Masimo)", "EVE": "Optionnel, réutilisable (Masimo)", "EVE IN": "Optionnel, réutilisable (Masimo)", "EVA NEO": "Optionnel, réutilisable (Masimo)", "EVE NEO": "Optionnel, réutilisable (Masimo)"}, {"Specifications": "Masques VNI", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Ballon test autoclavable (Ballon respiratoire)", "Monnal TEO": "Non", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Valve expiratoire de rechange reutilisable", "Monnal TEO": "O<PERSON> ", "SOPHIE": "O<PERSON> ", "EVA": "O<PERSON> ", "EVE": "O<PERSON> ", "EVE IN": "O<PERSON> ", "EVA NEO": "O<PERSON> ", "EVE NEO": "O<PERSON> "}, {"Specifications": "Humidifica<PERSON><PERSON> chauffant", "Monnal TEO": "Option ", "SOPHIE": "<PERSON><PERSON>, Intégré à l'unité patient P7", "EVA": "Option", "EVE": "Option ", "EVE IN": "Option ", "EVA NEO": "Option", "EVE NEO": "Option "}]}, {"Caractéristiques uniques VS autres modèles": [{"Specifications": "Modes de ventilation spéciaux de l'appareils", "Monnal TEO": null, "SOPHIE": "Oscillation à haute fréquence (HFO) avec modes synchronisés (sHFO) en option", "EVA": "VPC-VG \nAI-PSV \nCompensation tube ETT\nBackup ", "EVE": "VPC-VG \nAI-PSV \nCompensation tube ETT\nBackup ", "EVE IN": "VPC-VG \nAI-PSV \nCompensation tube ETT\nBackup ", "EVA NEO": "VPC-VG \nAI-PSV \nCompensation tube ETT\nBackup ", "EVE NEO": "VPC-VG \nAI-PSV \nCompensation tube ETT\nBackup "}, {"Specifications": "….....", "Monnal TEO": null, "SOPHIE": "sHFO, VACI-ITT, backup", "EVA": null, "EVE": "Compatible avec les transports (hélicoptère/ambulance)", "EVE IN": null, "EVA NEO": null, "EVE NEO": "conçu pour la ventilation d'urgence et le transport"}, {"Specifications": "….....", "Monnal TEO": null, "SOPHIE": "Modes de ventilation avec trigger externe ", "EVA": null, "EVE": "Turbine intégrée pour l'alimentation en air", "EVE IN": null, "EVA NEO": null, "EVE NEO": null}, {"Specifications": "….....", "Monnal TEO": null, "SOPHIE": "Humidificateur intégré  ", "EVA": null, "EVE": null, "EVE IN": null, "EVA NEO": null, "EVE NEO": null}, {"Specifications": "….....", "Monnal TEO": null, "SOPHIE": "Système d'auto-remplisage en option", "EVA": null, "EVE": null, "EVE IN": null, "EVA NEO": null, "EVE NEO": null}, {"Specifications": "….....", "Monnal TEO": null, "SOPHIE": "Unité patient P7", "EVA": null, "EVE": null, "EVE IN": null, "EVA NEO": null, "EVE NEO": null}]}]}