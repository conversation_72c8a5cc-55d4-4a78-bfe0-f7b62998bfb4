import pandas as pd
import json
from typing import List, Dict, Any

class ChunkedProductComparator:
    """
    Comparator for product specifications that processes data in chunks
    for more accurate comparisons
    """
    
    def __init__(self, llm_provider, chunk_size=10):
        """
        Initialize the chunked comparator
        
        Args:
            llm_provider: The LLM provider to use for comparison
            chunk_size: Number of features to include in each chunk
        """
        self.llm_provider = llm_provider
        self.chunk_size = chunk_size
    
    def load_comparison_data(self, json_path):
        """
        Load comprehensive comparison data from JSON

        Args:
            json_path: Path to the JSON file with detailed product specifications

        Returns:
            DataFrame: Pandas DataFrame with the comparison data
        """
        try:
            return pd.read_json(json_path)
        except Exception as e:
            print(f"Error loading comparison data: {e}")
            return pd.DataFrame()
    
    def chunk_specifications(self, df):
        """
        Split the specifications into chunks
        
        Args:
            df: DataFrame with specifications
            
        Returns:
            List of DataFrames, each containing a chunk of specifications
        """
        chunks = []
        # Skip the header row (if it exists)
        start_row = 1 if "Modes de ventilation:" in df.iloc[0]['Spécifications'] else 0
        
        # Get the total number of actual specification rows
        total_specs = len(df) - start_row
        
        # Create chunks
        for i in range(start_row, len(df), self.chunk_size):
            end_idx = min(i + self.chunk_size, len(df))
            chunks.append(df.iloc[i:end_idx].copy())
        
        return chunks
    
    def compare_with_chunks(self, parsed_specs, available_products, comparison_data_path):
        """
        Compare parsed specifications with available products using chunked approach
        
        Args:
            parsed_specs (dict): Parsed specifications from user
            available_products (list): List of available products
            comparison_data_path (str): Path to the comprehensive comparison data
            
        Returns:
            dict: Comparison results
        """
        # Initialize results structure
        results = {
            "products": [],
            "best_match": None,
            "best_match_score": 0,
            "detailed_analysis": {}
        }
        
        # Load comprehensive comparison data
        comparison_df = self.load_comparison_data(comparison_data_path)
        if comparison_df.empty:
            print("Warning: Comparison data is empty, falling back to basic comparison")
            return self._fallback_compare(parsed_specs, available_products)
        
        # Split into chunks
        chunks = self.chunk_specifications(comparison_df)
        
        # Process each product
        for product in available_products:
            product_name = product.get('Nom', '')
            
            # Initialize product comparison results
            product_comparisons = []
            match_count = 0
            total_count = 0
            
            # Process each chunk
            for chunk_idx, chunk in enumerate(chunks):
                # Convert chunk to dictionary format for the product
                product_chunk = self._create_product_chunk(chunk, product_name)
                
                # Compare this chunk with user specifications
                chunk_comparison = self._compare_chunk(parsed_specs, product_chunk, chunk_idx)
                
                # Accumulate results
                if chunk_comparison:
                    product_comparisons.extend(chunk_comparison.get('comparisons', []))
                    match_count += chunk_comparison.get('match_count', 0)
                    total_count += chunk_comparison.get('total_count', 0)
            
            # Calculate match percentage
            match_percentage = (match_count / total_count * 100) if total_count > 0 else 0
            
            # Add product comparison to results
            product_result = {
                "name": product_name,
                "description": product.get('Description', ''),
                "match_count": match_count,
                "total_count": total_count,
                "match_percentage": round(match_percentage, 2),
                "comparisons": product_comparisons
            }
            
            results["products"].append(product_result)
            
            # Update best match if this product has a higher match percentage
            if product_result["match_percentage"] > results["best_match_score"]:
                results["best_match"] = product_name
                results["best_match_score"] = product_result["match_percentage"]
            
            # Store detailed analysis
            results["detailed_analysis"][product_name] = {
                "match_count": match_count,
                "total_count": total_count,
                "match_percentage": match_percentage,
                "comparisons": product_comparisons
            }
        
        return results
    
    def _create_product_chunk(self, chunk_df, product_name):
        """
        Create a dictionary of product specifications from a chunk
        
        Args:
            chunk_df: DataFrame chunk with specifications
            product_name: Name of the product to extract
            
        Returns:
            dict: Product specifications for this chunk
        """
        product_chunk = {}
        
        # Find the column index for the product
        if product_name in chunk_df.columns:
            product_col = product_name
        else:
            # If exact name not found, try to find a close match
            for col in chunk_df.columns:
                if product_name in col:
                    product_col = col
                    break
            else:
                # If still not found, default to the first product column
                product_col = chunk_df.columns[1] if len(chunk_df.columns) > 1 else None
        
        if product_col:
            # Extract specifications for this product
            for _, row in chunk_df.iterrows():
                spec_name = row['Spécifications']
                if pd.notna(spec_name) and spec_name and ':' not in spec_name:  # Skip headers
                    spec_value = row[product_col]
                    if pd.notna(spec_value):
                        product_chunk[spec_name] = spec_value
        
        return product_chunk
    
    def _compare_chunk(self, parsed_specs, product_chunk, chunk_idx):
        """
        Compare a chunk of product specifications with user specifications
        
        Args:
            parsed_specs: User specifications
            product_chunk: Dictionary of product specifications for this chunk
            chunk_idx: Index of the current chunk
            
        Returns:
            dict: Comparison results for this chunk
        """
        # Skip empty chunks
        if not product_chunk:
            return None
        
        # Create a prompt that focuses on the current chunk
        prompt = f"""
        Compare the following user specifications with a chunk of product specifications.
        
        User Specifications:
        {json.dumps(parsed_specs, ensure_ascii=False, indent=2)}
        
        Product Specifications (Chunk {chunk_idx + 1}):
        {json.dumps(product_chunk, ensure_ascii=False, indent=2)}
        
        For each criterion in the user specifications, determine if it matches the product specifications in this chunk.
        Only compare criteria that are relevant to this chunk of product specifications.
        Provide detailed reasoning for each comparison.
        
        Return the result as a JSON object with the following structure:
        {{
            "comparisons": [
                {{
                    "criterion": "criterion_name",
                    "user_value": "user_value",
                    "product_value": "product_value",
                    "match": true/false,
                    "reasoning": "detailed reasoning"
                }},
                ...
            ],
            "match_count": number_of_matches,
            "total_count": total_number_of_criteria_compared,
            "match_percentage": percentage_of_matches
        }}
        """
        
        # Use LLM to compare
        response = self.llm_provider.generate_response(prompt)
        
        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, return a basic structure
                return {
                    "comparisons": [],
                    "match_count": 0,
                    "total_count": 0,
                    "match_percentage": 0
                }
        except Exception as e:
            print(f"Error comparing chunk {chunk_idx}: {e}")
            return {
                "comparisons": [],
                "match_count": 0,
                "total_count": 0,
                "match_percentage": 0,
                "error": str(e)
            }
    
    def _fallback_compare(self, parsed_specs, available_products):
        """
        Fallback to basic comparison if chunked comparison fails
        
        Args:
            parsed_specs: User specifications
            available_products: List of available products
            
        Returns:
            dict: Comparison results
        """
        results = {
            "products": [],
            "best_match": None,
            "best_match_score": 0,
            "detailed_analysis": {}
        }
        
        for product in available_products:
            product_name = product.get('Nom', '')

            # Extract only specification fields (exclude 'Nom' and 'Description')
            product_specs = {k: v for k, v in product.items()
                           if k not in ['Nom', 'Description']}

            # Compare specifications with this product
            comparison = self.llm_provider.compare_specifications(parsed_specs, product_specs)

            # Add product comparison to results
            product_result = {
                "name": product_name,
                "description": product.get('Description', ''),
                "match_count": comparison.get('match_count', 0),
                "total_count": comparison.get('total_count', 0),
                "match_percentage": comparison.get('match_percentage', 0),
                "comparisons": comparison.get('comparisons', [])
            }
            
            results["products"].append(product_result)
            
            # Update best match if this product has a higher match percentage
            if product_result["match_percentage"] > results["best_match_score"]:
                results["best_match"] = product_name
                results["best_match_score"] = product_result["match_percentage"]
            
            # Store detailed analysis
            results["detailed_analysis"][product_name] = comparison
        
        return results
