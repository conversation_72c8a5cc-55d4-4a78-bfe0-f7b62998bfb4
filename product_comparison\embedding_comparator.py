import json
import os

class EmbeddingBasedComparator:
    """
    Comparator for product specifications using embeddings to select relevant specs
    """
    
    def __init__(self, llm_provider, embedding_selector):
        """
        Initialize the comparator
        
        Args:
            llm_provider: The LLM provider to use for comparison
            embedding_selector: The embedding-based selector for specifications
        """
        self.llm_provider = llm_provider
        self.embedding_selector = embedding_selector
    
    def load_product_specs(self, file_path='products_specs.json'):
        """
        Load product specifications from JSON file
        
        Args:
            file_path (str): Path to the product specs JSON file
            
        Returns:
            dict: Dictionary with product specifications
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"Error loading product specs: {e}")
            return {}
    
    def extract_product_specs_for_comparison(self, product_specs_data, selected_specs, product_name):
        """
        Extract product specifications for a specific product based on selected specs
        
        Args:
            product_specs_data (dict): Full product specifications data
            selected_specs (dict): Selected specifications grouped by category
            product_name (str): Name of the product to extract specs for
            
        Returns:
            dict: Dictionary with extracted product specifications
        """
        extracted_specs = {}
        
        for group_dict in product_specs_data.get('groups', []):
            for group_name, specs_list in group_dict.items():
                if group_name in selected_specs:
                    for spec_item in specs_list:
                        spec_name = spec_item.get('Specifications')
                        if spec_name in selected_specs[group_name]:
                            product_value = spec_item.get(product_name, '')
                            extracted_specs[spec_name] = product_value
        
        return extracted_specs
    
    def compare(self, query_text, available_products):
        """
        Compare query with available products using embedding-based selection
        
        Args:
            query_text (str): The query text from the user
            available_products (list): List of available products
            
        Returns:
            dict: Comparison results
        """
        # Initialize results structure
        results = {
            "products": [],
            "best_match": None,
            "best_match_score": 0,
            "detailed_analysis": {},
            "selected_specs": {}
        }
        
        # Select relevant specifications using embeddings
        selected_specs = self.embedding_selector.select_relevant_specs(query_text)
        results["selected_specs"] = selected_specs
        
        if not selected_specs:
            print("No relevant specifications found.")
            return results
        
        # Load product specifications
        product_specs_data = self.load_product_specs()
        if not product_specs_data:
            print("Failed to load product specifications.")
            return results
        
        # Parse the query text to get structured specifications
        parsed_specs = self.llm_provider.parse_specifications(query_text)
        
        # Compare with each product
        for product in available_products:
            product_name = product.get('Nom', '')
            
            # Extract product specifications for comparison
            product_specs = self.extract_product_specs_for_comparison(
                product_specs_data, selected_specs, product_name
            )
            
            # Compare specifications with this product
            comparison = self.llm_provider.compare_specifications(parsed_specs, product_specs)
            
            # Add product comparison to results
            product_result = {
                "name": product_name,
                "description": product.get('Description', ''),
                "match_count": comparison.get('match_count', 0),
                "total_count": comparison.get('total_count', 0),
                "match_percentage": comparison.get('match_percentage', 0),
                "comparisons": comparison.get('comparisons', [])
            }
            
            results["products"].append(product_result)
            
            # Update best match if this product has a higher match percentage
            match_percentage = comparison.get('match_percentage', 0)
            if match_percentage > results["best_match_score"]:
                results["best_match"] = product_name
                results["best_match_score"] = match_percentage
                results["detailed_analysis"] = comparison
        
        return results
