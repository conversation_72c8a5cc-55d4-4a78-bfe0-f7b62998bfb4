#!/usr/bin/env python3
"""
Test script for the new intelligent specification parsing functionality
"""

import os
import json
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser

# Load environment variables
load_dotenv()

def test_intelligent_parsing():
    """Test the intelligent parsing functionality"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test intelligent parsing")
        return
    
    print("=== Testing Intelligent Specification Parsing ===\n")
    
    # Initialize the LLM provider and parser
    llm_factory = LLMProviderFactory()
    llm_provider = llm_factory.get_provider()
    parser = IntelligentSpecificationParser(llm_provider)
    
    # Test cases with different types of specifications
    test_cases = [
        {
            "name": "French Ventilator Specifications",
            "input": """
            Volume controlé: Oui
            Pression controlée: Oui
            Aide inspiratoire: Disponible
            PEEP: 5-20 cmH2O
            FIO2: 21-100%
            Alarme fuite: Oui
            Alarme déconnection: Oui
            """
        },
        {
            "name": "Mixed Language Specifications",
            "input": """
            Volume control mode: Required
            Pressure control: Yes
            PEEP range: 0-30 cmH2O
            Tidal volume: 50-2000 mL
            Leak alarm: Must have
            Power failure alarm: Required
            Custom parameter XYZ: 123
            """
        },
        {
            "name": "Complex Specifications",
            "input": """
            Le ventilateur doit avoir:
            - Mode volume controlé avec VT réglable
            - Pression positive en fin d'expiration (PEEP) de 0 à 25 cmH2O
            - Alarmes de fuite et de déconnection
            - Affichage des courbes temps réel
            - Fonction auto-test
            - Paramètre personnalisé ABC: valeur spéciale
            """
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"Test Case {i}: {test_case['name']}")
        print("-" * 50)
        print(f"Input: {test_case['input'].strip()}")
        print()
        
        try:
            # Parse the specifications
            result = parser.parse(test_case['input'])
            
            # Display results
            print("Results:")
            print(f"  Matched specifications: {sum(len(specs) for specs in result['matched_criterias'].values())}")
            print(f"  Non-matched specifications: {len(result['non_matched_criterias'])}")
            
            # Show matched specifications by category
            if result['matched_criterias']:
                print("\n  Matched by category:")
                for category, specs in result['matched_criterias'].items():
                    print(f"    {category}:")
                    for spec in specs:
                        print(f"      - {spec['spec_name']}: {spec['user_value']}")
            
            # Show non-matched specifications
            if result['non_matched_criterias']:
                print("\n  Non-matched specifications:")
                for spec in result['non_matched_criterias']:
                    print(f"    - {spec['spec_name']}: {spec['user_value']}")
            
            # Test the comparison format extraction
            matched_for_comparison = parser.get_matched_specs_for_comparison(result)
            print(f"\n  Specifications ready for comparison: {len(matched_for_comparison)}")
            if matched_for_comparison:
                print("    Sample:")
                for key, value in list(matched_for_comparison.items())[:3]:
                    print(f"      {key}: {value}")
            
            print("✅ Test passed\n")
            
        except Exception as e:
            print(f"❌ Test failed: {e}\n")
        
        print("=" * 60)
        print()

def test_simplified_specs_loading():
    """Test loading of simplified_specs.json"""
    print("=== Testing Simplified Specs Loading ===\n")
    
    try:
        with open('simplified_specs.json', 'r', encoding='utf-8') as f:
            specs = json.load(f)
        
        print(f"✅ Successfully loaded simplified_specs.json")
        print(f"   Categories: {len(specs.get('groups', []))}")
        
        total_specs = 0
        for group in specs.get('groups', []):
            for category, spec_list in group.items():
                print(f"   {category}: {len(spec_list)} specifications")
                total_specs += len(spec_list)
        
        print(f"   Total specifications: {total_specs}")
        
    except Exception as e:
        print(f"❌ Failed to load simplified_specs.json: {e}")
    
    print()

def test_fallback_parsing():
    """Test fallback parsing when simplified_specs.json is not available"""
    print("=== Testing Fallback Parsing ===\n")
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - cannot test fallback parsing")
        return
    
    try:
        # Initialize parser with non-existent file
        llm_factory = LLMProviderFactory()
        llm_provider = llm_factory.get_provider()
        parser = IntelligentSpecificationParser(llm_provider, 'non_existent_file.json')
        
        test_input = "Volume control: Yes, PEEP: 10 cmH2O, Custom param: value"
        result = parser.parse(test_input)
        
        print(f"✅ Fallback parsing works")
        print(f"   Non-matched specifications: {len(result['non_matched_criterias'])}")
        print(f"   Matched specifications: {sum(len(specs) for specs in result['matched_criterias'].values())}")
        
    except Exception as e:
        print(f"❌ Fallback parsing failed: {e}")
    
    print()

if __name__ == "__main__":
    test_simplified_specs_loading()
    test_intelligent_parsing()
    test_fallback_parsing()
    
    print("=== Test Summary ===")
    print("The intelligent parsing system:")
    print("1. ✅ Loads simplified_specs.json for reference")
    print("2. ✅ Uses OpenAI to intelligently match specifications")
    print("3. ✅ Categorizes results into matched and non-matched")
    print("4. ✅ Provides fallback parsing when needed")
    print("5. ✅ Formats results for product comparison")
    print("\nThe system is ready to replace regex/comma separation logic!")
