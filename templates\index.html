<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Specification Comparison</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Product Specification Comparison</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Enter Product Specifications</h3>
                    </div>
                    <div class="card-body">
                        <form id="specificationForm">
                            <div class="mb-3">
                                <label for="specifications" class="form-label">Product Specifications</label>
                                <textarea class="form-control" id="specifications" rows="10" placeholder="Enter product specifications here..."></textarea>
                                <div class="form-text">Enter each specification on a new line or in paragraph format.</div>
                            </div>

                            <div class="mb-3">
                                <div class="alert alert-info">
                                    <strong>AI Provider:</strong> OpenAI GPT-4.1-mini with text-embedding-3-large
                                    <br><small>128K context window with maximum performance configuration</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="useEmbeddings" checked>
                                    <label class="form-check-label" for="useEmbeddings">
                                        Use embedding-based specification selection
                                    </label>
                                    <div class="form-text">Uses embeddings to select relevant specifications from simplified_specs.json</div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="useChunked">
                                    <label class="form-check-label" for="useChunked">
                                        Use chunked comparison (legacy)
                                    </label>
                                    <div class="form-text">Only used if embedding-based selection is disabled</div>
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">Compare Specifications</button>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3>Available Products</h3>
                    </div>
                    <div class="card-body">
                        <ul class="list-group">
                            <li class="list-group-item">Monnal TEO</li>
                            <li class="list-group-item">SOPHIE</li>
                            <li class="list-group-item">EVA</li>
                            <li class="list-group-item">EVE</li>
                            <li class="list-group-item">EVE IN</li>
                            <li class="list-group-item">EVA NEO</li>
                            <li class="list-group-item">EVE NEO</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card d-none" id="resultsCard">
                    <div class="card-header">
                        <h3>Comparison Results</h3>
                    </div>
                    <div class="card-body">
                        <div id="loadingSpinner" class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p>Analyzing specifications...</p>
                        </div>

                        <div id="resultsContent" class="d-none">
                            <div class="alert alert-success" id="bestMatchAlert">
                                <h4>Best Match: <span id="bestMatchProduct"></span></h4>
                                <p>Match Score: <span id="bestMatchScore"></span>%</p>
                            </div>

                            <h4>Product Comparison Results</h4>
                            <div id="productResults"></div>

                            <h4 class="mt-4">Detailed Analysis</h4>
                            <div id="detailedAnalysis"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
</body>
</html>
