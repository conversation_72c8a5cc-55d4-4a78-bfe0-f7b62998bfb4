#!/usr/bin/env python3
"""
Example showing how to use different OpenAI models with the application
"""

import os
from dotenv import load_dotenv
from llm_providers.openai_provider import OpenAIProvider

# Load environment variables
load_dotenv()

def demonstrate_models():
    """Demonstrate the different supported models"""
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - showing configuration only")
        print("\nSupported Models:")
        print("1. gpt-4.1-mini (default) - Balanced performance and cost")
        print("2. gpt-4.1-nano - Optimized for speed and efficiency")
        print("\nBoth models support:")
        print("- 128K context window")
        print("- 16,384 max completion tokens")
        print("- JSON response format")
        return
    
    print("=== OpenAI Model Demonstration ===\n")
    
    # Test prompt
    test_prompt = """
    Please respond with a JSON object containing:
    {
        "model_info": "information about the current model",
        "context_window": "128K tokens",
        "completion_tokens": "16384 max",
        "use_case": "description of when to use this model"
    }
    """
    
    # Test GPT-4.1-mini (default)
    print("1. Testing GPT-4.1-mini (default):")
    print("-" * 40)
    try:
        mini_provider = OpenAIProvider()  # Uses default gpt-4.1-mini
        print(f"Model: {mini_provider.model}")
        print(f"Max tokens: {mini_provider.max_tokens_map[mini_provider.model]}")
        
        response = mini_provider.generate_response(test_prompt, temperature=0)
        print(f"Response: {response[:200]}...")
        print("✅ GPT-4.1-mini test successful\n")
    except Exception as e:
        print(f"❌ GPT-4.1-mini test failed: {e}\n")
    
    # Test GPT-4.1-nano
    print("2. Testing GPT-4.1-nano:")
    print("-" * 40)
    try:
        nano_provider = OpenAIProvider(model="gpt-4.1-nano")
        print(f"Model: {nano_provider.model}")
        print(f"Max tokens: {nano_provider.max_tokens_map[nano_provider.model]}")
        
        response = nano_provider.generate_response(test_prompt, temperature=0)
        print(f"Response: {response[:200]}...")
        print("✅ GPT-4.1-nano test successful\n")
    except Exception as e:
        print(f"❌ GPT-4.1-nano test failed: {e}\n")

def show_model_comparison():
    """Show comparison between the two models"""
    print("=== Model Comparison ===\n")
    
    comparison = {
        "Feature": ["Context Window", "Max Completion Tokens", "Speed", "Cost", "Use Case"],
        "GPT-4.1-mini": [
            "128K tokens",
            "16,384 tokens", 
            "Balanced",
            "Moderate",
            "General purpose, balanced performance"
        ],
        "GPT-4.1-nano": [
            "128K tokens",
            "16,384 tokens",
            "Faster",
            "Lower",
            "Speed-optimized, cost-effective"
        ]
    }
    
    # Print comparison table
    print(f"{'Feature':<20} {'GPT-4.1-mini':<35} {'GPT-4.1-nano':<35}")
    print("-" * 90)
    
    for i, feature in enumerate(comparison["Feature"]):
        mini_value = comparison["GPT-4.1-mini"][i]
        nano_value = comparison["GPT-4.1-nano"][i]
        print(f"{feature:<20} {mini_value:<35} {nano_value:<35}")

if __name__ == "__main__":
    demonstrate_models()
    show_model_comparison()
    
    print("\n=== How to Switch Models ===")
    print("To change the default model, modify the OpenAIProvider initialization:")
    print("```python")
    print("# For GPT-4.1-mini (default)")
    print("provider = OpenAIProvider()")
    print("# or explicitly")
    print("provider = OpenAIProvider(model='gpt-4.1-mini')")
    print("")
    print("# For GPT-4.1-nano")
    print("provider = OpenAIProvider(model='gpt-4.1-nano')")
    print("```")
