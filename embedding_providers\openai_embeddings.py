import os
import numpy as np
from openai import OpenAI
from .base_provider import BaseEmbeddingProvider

class OpenAIEmbeddingProvider(BaseEmbeddingProvider):
    """OpenAI embedding provider implementation"""

    def __init__(self, model="text-embedding-3-small"):
        """
        Initialize OpenAI embedding provider
        
        Args:
            model (str): The OpenAI embedding model to use
        """
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable not set")
        
        self.model = model
        self.client = OpenAI(api_key=self.api_key)
    
    def get_embeddings(self, texts):
        """
        Get embeddings for a list of texts using OpenAI
        
        Args:
            texts (list): List of text strings to embed
            
        Returns:
            list: List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            response = self.client.embeddings.create(
                model=self.model,
                input=texts
            )
            
            # Extract embeddings from response
            embeddings = [item.embedding for item in response.data]
            return embeddings
        
        except Exception as e:
            print(f"Error getting embeddings from OpenAI: {e}")
            return [[] for _ in texts]  # Return empty embeddings on error
    
    def compute_similarity(self, embedding1, embedding2):
        """
        Compute cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Cosine similarity score (higher means more similar)
        """
        if not embedding1 or not embedding2:
            return 0.0
        
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        # Compute cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
