from .openai_provider import OpenAIProvider

class LLMProviderFactory:
    """Factory for creating OpenAI LLM providers"""

    def get_provider(self, provider_name=None):
        """
        Get an OpenAI LLM provider (only OpenAI is supported)

        Args:
            provider_name (str): Ignored - only OpenAI is supported

        Returns:
            OpenAIProvider: An instance of the OpenAI provider
        """
        return OpenAIProvider()
