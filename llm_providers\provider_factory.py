from .openai_provider import OpenAIProvider
from .llama_provider import LlamaProvider

class LLMProviderFactory:
    """Factory for creating LLM providers"""
    
    def get_provider(self, provider_name):
        """
        Get an LLM provider by name
        
        Args:
            provider_name (str): The name of the provider ('openai' or 'llama')
            
        Returns:
            BaseLLMProvider: An instance of the requested provider
        """
        if provider_name.lower() == 'openai':
            return OpenAIProvider()
        elif provider_name.lower() == 'llama':
            return LlamaProvider()
        else:
            raise ValueError(f"Unknown provider: {provider_name}")
