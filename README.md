# Product Specification Comparison App

This Flask application uses OpenAI's most powerful LLMs (Large Language Models) and embeddings to analyze product specifications and compare them with a list of available products. **Optimized for maximum performance with OpenAI GPT-4.1-mini and text-embedding-3-large.**

## Features

- **Maximum OpenAI Performance**: Uses GPT-4.1-mini (default) with 128K context window and 16K completion tokens, plus text-embedding-3-large embeddings
- **Multiple Model Support**: Supports both GPT-4.1-mini and GPT-4.1-nano (both with 128K context)
- **Optimized Token Processing**: Uses model maximum completion tokens (16,384) - no artificial limits
- **Intelligent Specification Parsing**: AI-powered extraction using simplified_specs.json as reference
- **Advanced Embedding-Based Selection**: Intelligent specification selection from simplified_specs.json
- **Smart Categorization**: Automatically separates matched and non-matched specifications
- **Precise Product Comparison**: Line-by-line comparison with detailed AI reasoning
- **JSON-Structured Output**: Detailed analysis and comparison results
- **Modern Web Interface**: Clean, responsive interface for easy interaction

## Available Products

The application comes with a predefined list of ventilator products:
- Monnal TEO
- SOPHIE
- EVA
- EVE
- EVE IN
- EVA NEO
- EVE NEO

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your API keys (see `.env.example`)
4. Run the application:
   ```
   python app.py
   ```

## Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `EMBEDDING_SIMILARITY_THRESHOLD`: Threshold for similarity score in embedding-based selection (default: 0.6)
- `EMBEDDINGS_FILE`: File to store/load embeddings from (default: spec_embeddings.pkl)

## Supported Models

The application supports the following OpenAI models with 128K context windows:

- **GPT-4.1-mini** (default): Balanced performance and cost
- **GPT-4.1-nano**: Optimized for speed and efficiency

To change the model, modify the `model` parameter in the `OpenAIProvider` initialization in `llm_providers/openai_provider.py`:

```python
# For GPT-4.1-nano
provider = OpenAIProvider(model="gpt-4.1-nano")

# For GPT-4.1-mini (default)
provider = OpenAIProvider(model="gpt-4.1-mini")
```

## Usage

1. Open the web interface at `http://localhost:5000`
2. Enter product specifications in the text area
3. Choose whether to use embedding-based specification selection (recommended)
4. Click "Compare Specifications"
5. View the results, including:
   - Selected specifications (when using embedding-based selection)
   - Best matching product
   - Match percentage for each product
   - Detailed comparison for each criterion
   - AI-powered reasoning for each match/mismatch

## How It Works

### OpenAI-Powered Analysis

The system offers two intelligent approaches for specification analysis:

#### 1. Intelligent Specification Parsing (New Default)

1. **Reference Loading**: The system loads all available specifications from `simplified_specs.json`
2. **AI-Powered Extraction**: When a user submits specifications, GPT-4.1-mini:
   - Analyzes the input text using the available specifications as reference
   - Intelligently matches user specifications with exact names from the reference
   - Handles multiple languages and semantic variations (e.g., "Volume Control" → "Volume controlé")
   - Categorizes specifications into matched and non-matched groups
3. **Smart Categorization**: Returns structured results with:
   - **Matched Criterias**: Specifications found in `simplified_specs.json` with exact reference names
   - **Non-Matched Criterias**: Specifications not available in the product database
4. **Product Comparison**: Uses only matched specifications for accurate product comparison

#### 2. Embedding-Based Selection (Alternative)

1. **Advanced Embeddings**: Uses OpenAI's text-embedding-3-large to generate high-quality embeddings
2. **Similarity Matching**: Compares user input with stored specifications using cosine similarity
3. **Threshold-Based Selection**: Selects specifications above the similarity threshold
4. **Comprehensive Analysis**: Performs detailed comparisons with AI-powered reasoning

Both approaches leverage OpenAI's most advanced models for maximum accuracy and detailed analysis, with no artificial limitations on processing capacity.

## Example Input

```
Volume controlé: Oui
Pression controlée: Oui
Aide inspiratoire (AI): Oui
Spontanée en PEP: Non
```

## Output Format

The application generates a JSON output with:
- Comparison results for each product
- Match count and percentage
- Detailed reasoning for each comparison
- Best matching product based on specifications

## License

MIT
#   c p s - a n a l y s i s 
 
 