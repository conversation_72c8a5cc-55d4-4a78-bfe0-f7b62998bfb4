# Product Specification Comparison App

This Flask application uses OpenAI's most powerful LLMs (Large Language Models) and embeddings to analyze product specifications and compare them with a list of available products. **Optimized for maximum performance with OpenAI GPT-4o and text-embedding-3-large.**

## Features

- **Maximum OpenAI Performance**: Uses GPT-4o with 128K context window and 16K completion tokens, plus text-embedding-3-large embeddings
- **Optimized Token Processing**: Uses model maximum completion tokens (16,384 for GPT-4o) - no artificial limits
- **Advanced Embedding-Based Selection**: Intelligent specification selection from simplified_specs.json
- **Precise Product Comparison**: Line-by-line comparison with detailed AI reasoning
- **JSON-Structured Output**: Detailed analysis and comparison results
- **Modern Web Interface**: Clean, responsive interface for easy interaction

## Available Products

The application comes with a predefined list of ventilator products:
- Monnal TEO
- SOPHIE
- EVA
- EVE
- EVE IN
- EVA NEO
- EVE NEO

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your API keys (see `.env.example`)
4. Run the application:
   ```
   python app.py
   ```

## Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key (required)
- `EMBEDDING_SIMILARITY_THRESHOLD`: Threshold for similarity score in embedding-based selection (default: 0.6)
- `EMBEDDINGS_FILE`: File to store/load embeddings from (default: spec_embeddings.pkl)

## Usage

1. Open the web interface at `http://localhost:5000`
2. Enter product specifications in the text area
3. Choose whether to use embedding-based specification selection (recommended)
4. Click "Compare Specifications"
5. View the results, including:
   - Selected specifications (when using embedding-based selection)
   - Best matching product
   - Match percentage for each product
   - Detailed comparison for each criterion
   - AI-powered reasoning for each match/mismatch

## How It Works

### OpenAI-Powered Analysis

1. **Specification Loading**: The system loads all specifications from `simplified_specs.json`
2. **Advanced Embeddings**: Uses OpenAI's text-embedding-3-large to generate high-quality embeddings for all specifications
3. **Intelligent Query Processing**: When a user submits a query, the system:
   - Splits the query into smaller, overlapping chunks for better matching
   - Generates embeddings for each chunk using OpenAI's embedding model
   - Compares embeddings with all stored specifications using cosine similarity
   - Selects the most relevant specifications above the similarity threshold
   - Extracts corresponding product specifications from `products_specs.json`
   - Uses GPT-4o with maximum token limits to perform detailed comparisons
   - Returns comprehensive analysis with AI-powered reasoning

This approach leverages OpenAI's most advanced models for maximum accuracy and detailed analysis, with no artificial limitations on processing capacity.

## Example Input

```
Volume controlé: Oui
Pression controlée: Oui
Aide inspiratoire (AI): Oui
Spontanée en PEP: Non
```

## Output Format

The application generates a JSON output with:
- Comparison results for each product
- Match count and percentage
- Detailed reasoning for each comparison
- Best matching product based on specifications

## License

MIT
#   c p s - a n a l y s i s 
 
 