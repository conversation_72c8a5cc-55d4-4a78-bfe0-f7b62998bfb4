# Product Specification Comparison App

This Flask application uses LLMs (Large Language Models) and embeddings to analyze product specifications and compare them with a list of available products. It supports both OpenAI and Llama models for processing, along with multiple embedding providers.

## Features

- Embedding-based selection of relevant specifications from simplified_specs.json
- Support for multiple embedding providers (OpenAI and Sentence Transformers)
- Parse product specifications using LLMs
- Compare specifications with available products
- Line-by-line comparison with detailed reasoning
- Support for multiple LLM providers (OpenAI and Llama)
- JSON output with detailed analysis and comparison steps
- Web interface for easy interaction

## Available Products

The application comes with a predefined list of ventilator products:
- Monnal TEO
- SOPHIE
- EVA
- EVE
- EVE IN
- EVA NEO
- EVE NEO

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Create a `.env` file with your API keys (see `.env.example`)
4. Run the application:
   ```
   python app.py
   ```

## Environment Variables

- `OPENAI_API_KEY`: Your OpenAI API key
- `LLAMA_API_URL`: URL for your Llama model API (OVH endpoint)
- `OVH_TOKEN`: Your OVH token for Llama API authentication
- `OPENAI_EMBEDDING_MODEL`: OpenAI embedding model to use (default: text-embedding-3-small)
- `SENTENCE_TRANSFORMER_MODEL`: Sentence Transformer model to use (default: all-MiniLM-L6-v2)
- `EMBEDDING_SIMILARITY_THRESHOLD`: Threshold for similarity score in embedding-based selection (default: 0.6)
- `EMBEDDINGS_FILE`: File to store/load embeddings from (default: spec_embeddings.pkl)

## Usage

1. Open the web interface at `http://localhost:5000`
2. Enter product specifications in the text area
3. Select the LLM provider (OpenAI or Llama)
4. Select the embedding provider (OpenAI or Sentence Transformer)
5. Choose whether to use embedding-based specification selection (recommended)
6. Click "Compare Specifications"
7. View the results, including:
   - Selected specifications (when using embedding-based selection)
   - Best matching product
   - Match percentage for each product
   - Detailed comparison for each criterion
   - Reasoning for each match/mismatch

## How It Works

### Embedding-Based Approach (New)

1. The system loads all specifications from `simplified_specs.json`
2. The system generates embeddings for all specifications and stores them in a file for reuse
3. When a user submits a query, the system:
   - Splits the query into smaller, overlapping chunks to improve matching
   - Generates embeddings for each chunk using the selected embedding provider
   - Compares each chunk's embedding with embeddings of all specifications
   - Keeps the highest similarity score for each specification across all chunks
   - Selects specifications that have a similarity score above the threshold
   - Extracts the corresponding product specifications from `products_specs.json`
   - Uses the LLM to compare the user's query with the selected specifications
   - Returns detailed comparison results

This approach is more efficient and focused, as it only compares specifications that are relevant to the user's query. By splitting the input into chunks, it can better identify relevant specifications even in longer, more complex queries.

## Example Input

```
Volume controlé: Oui
Pression controlée: Oui
Aide inspiratoire (AI): Oui
Spontanée en PEP: Non
```

## Output Format

The application generates a JSON output with:
- Comparison results for each product
- Match count and percentage
- Detailed reasoning for each comparison
- Best matching product based on specifications

## License

MIT
#   c p s - a n a l y s i s 
 
 