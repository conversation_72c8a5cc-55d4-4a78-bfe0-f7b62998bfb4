{"parsing_result": {"matched_criterias": {"Modes de ventilation": [{"spec_name": "Volume controlé", "cps_spec_requirement": "La ventilation en volume contrôlée", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "A/C VCV", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "VC-VC", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "VC-CMV, VC-S-IMV", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "VC-VC, VC-VACI", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "VC-VC, VC-VACI", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "VC-VC", "fulfills_requirement": true}]}, {"spec_name": "Pression controlée", "cps_spec_requirement": "La ventilation assistée - contrôlée en pression", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "A/C PC", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "VCI", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "VC-VC", "fulfills_requirement": false}, {"product_name": "EVE", "product_value": "PC-VC, nPC-VC, PC-VAC, nPC-VAC", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "PC-VC, PC-VACI, PC-VAC, PC-VAC+", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "PC-VC, PC-VACI, PC-VAC", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "PC-VC", "fulfills_requirement": true}]}, {"spec_name": "Assistée - controlée intermittente en volume", "cps_spec_requirement": "La ventilation assistée - contrôlée intermittente en volume", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "VC-VACI", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "VC-S-IMV", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "VC-VACI", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "VC-VACI", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "VC-VACI", "fulfills_requirement": true}]}, {"spec_name": "Assistée - controlée intermittente en pression", "cps_spec_requirement": "La ventilation assistée - contrôlée intermittente à pression contrôlée", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "PSIMV", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "VACI", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "PC-VACI", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "PC-VACI, nPC-VACI", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "PC-VACI", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "PC-VACI", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "PC-VACI", "fulfills_requirement": true}]}, {"spec_name": "À 2 niveaux de pression", "cps_spec_requirement": "La ventilation à deux niveaux de pression positive Bi-Level", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "DUO-Levels", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "DUOPAP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "DUOPAP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "DUOPAP", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "DUOPAP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "DUOPAP", "fulfills_requirement": true}]}, {"spec_name": "Intelligente à aide adaptative", "cps_spec_requirement": "La ventilation intelligente à aide adaptative", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "PS-PRO", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE IN", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA NEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE NEO", "product_value": "Non", "fulfills_requirement": false}]}, {"spec_name": "Pression positive à relachement de pression", "cps_spec_requirement": "La ventilation en pression positive à relâchements de pression", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "non", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE IN", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA NEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE NEO", "product_value": "Non", "fulfills_requirement": false}]}, {"spec_name": "Non invasive", "cps_spec_requirement": "La ventilation non invasive", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "VPPNI, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nDUOPAP, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nVS-PEP, nDUOPAP", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "nPC-VC, nPC-VACI, nPC-VAC, nPC-VAC+, nDUOPAP, nVS-PEP", "fulfills_requirement": true}]}, {"spec_name": "<PERSON><PERSON> (AI)", "cps_spec_requirement": "L’aide inspiratoire", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "PSV", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "PPSV% p33", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "AI-PSV", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "AI-PSV", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "Oui (VS-PEP+AI-PSV)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "AI-PSV (disponible avec PC-VACI, VC-VACI, VSPEP et DUOPAP)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "OUI (AI-PSV)", "fulfills_requirement": true}]}, {"spec_name": "Spontanée en PEP", "cps_spec_requirement": "La ventilation spontanée ou PEEP", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "PSV, PS-Pro", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "VS-PEP, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "VS-PEP, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "VSPEP, NI-VSPEP", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "VS-PEP, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "VS-PEP, nVS-PEP", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "VS-PEP, nVS-PEP", "fulfills_requirement": true}]}, {"spec_name": "Oxygénothérapie", "cps_spec_requirement": "Thérapie pour oxygène (Oxygénothérapie)", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "<PERSON><PERSON> (HFO2)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "<PERSON><PERSON> (High-Flow)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}], "Paramètres": [{"spec_name": "Écran (pouces)", "cps_spec_requirement": "Ecran couleur d’au moins 7 pouces intégré au respirateur avec une résolution de 640×480 pixels au minimum.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "15 pouces", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "12.1", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "12.2", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "8,4\"", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "8,4\"", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "12.2", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "8,4\"", "fulfills_requirement": true}]}, {"spec_name": "Courbes temps réel (nombre)", "cps_spec_requirement": "Courbes en temps réel : pression, débit et volume", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "4", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "5", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "5", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "5", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "5", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "5", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "5", "fulfills_requirement": true}]}, {"spec_name": "<PERSON><PERSON><PERSON> (nombre)", "cps_spec_requirement": "Boucles : volume/pression et Débit/volume", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "3", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "3", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "3", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "3", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "3", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "3", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "3", "fulfills_requirement": true}]}, {"spec_name": "Pinsp", "cps_spec_requirement": "L’aide inspiratoire : 0 à 60 cmH2O minimum", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "2-99 cmH2O", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Pmax: 5 à 60 mbar", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "1-55 mbar(neo/ped), 1-95 (adulte)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "1-55 mbar(neo/ped), 1-95 (adulte)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "1-55 mbar(neo/ped), 1-95 (adulte)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "1-55 mbar(neo/ped), 1-95 (adulte)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "1-55 mbar(neo/ped), 1-95 (adulte)", "fulfills_requirement": true}]}, {"spec_name": "PEEP", "cps_spec_requirement": "Pression expiratoire (PEEP électronique intégré) : de 0 à 35 cm H2O minimum", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "0-50 cm H2O", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "0-30 mbar", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "0-35 mbar", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "0-35 mbar", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "0-35 (mode neo), 0-35 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "0-35 mbar", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "0-35 (mode neo), 0-35 (ped/adulte)", "fulfills_requirement": true}]}, {"spec_name": "Ftot", "cps_spec_requirement": "Fréquence respiratoire : de 1 à 80 cycles/min minimum", "products_evaluation": [{"product_name": "SOPHIE", "product_value": "1-300/min", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "1-200 (mode neo), 1-150 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "1-200 (mode neo), 1-150 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "1-200 (mode neo), 1-150 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "1-200 (mode neo), 1-150 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "1-200 (mode neo), 1-150 (ped/adulte)", "fulfills_requirement": true}, {"product_name": "Monnal TEO", "product_value": "Not specified in Ftot", "fulfills_requirement": false}]}, {"spec_name": "Ti", "cps_spec_requirement": "Temps inspiratoire : 0,1 à 12 Sec minimum", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "0,2-10s", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "0,1-2 s", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "0,15 à30 sec. (NEO-Mode), 0,2 à 30 sec. (Ped./Adult-Mode)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "0,15 à 30 s(neo), 0,2 à 30 s(ped/adlt)", "fulfills_requirement": true}]}, {"spec_name": "Rapport I:E", "cps_spec_requirement": "Rapport I/E modulable", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "1:0.2-1:19", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "1:199 ... 199:1 (Mode NEO) 1:150 ... 150:1 (Mode péd./adulte)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "1:150 ... 150:1 (Adult-Mode) 1:200 ... 200:1 (NEO/Ped.-Mode)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "1:150 ... 150:1 (Adult-Mode) 1:200 ... 200:1 (NEO/Ped.-Mode)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "1:199/199:1 (Néo)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "1:150 ... 150:1 (Adult/Ped) 1:200 ... 200:1 (NEO)", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Not specified", "fulfills_requirement": false}]}, {"spec_name": "FIO2", "cps_spec_requirement": "FIO2 de 21% à 100% minimum", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "30-60%", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "21-100 %", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "21-100 %", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "21-100 %", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "21-100 %", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "21-100 %", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "21-100 %", "fulfills_requirement": true}]}, {"spec_name": "Débit insp", "cps_spec_requirement": "Débit inspiratoire : 0 à 260 l/min minimum", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "0-150 L/min", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "de -200 à 200 l/min", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "de -200 à 200 l/min", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "de -200 à 200 l/min", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "de -200 à 200 l/min", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "de -200 à 200 l/min", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Not specified", "fulfills_requirement": false}]}, {"spec_name": "VT", "cps_spec_requirement": "Volume courant : de 2 à 2000 ml", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "adulte: 100-2000ml enfant : 50-500ml nourrissons : 20-75", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "(VtTar) 2-150 ml", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "2-2000 ml", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "2-2000 ml", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "2-2000 ml", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "2-2000 ml", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "2-2000 ml", "fulfills_requirement": true}]}], "Alarmes: (oui/ message affiché)": [{"spec_name": "Alarmes code couleur", "cps_spec_requirement": "Sonore et visuelle avec des indicateurs de la priorité (Haute, Moyenne et Basse)", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "<PERSON> (HP), <PERSON><PERSON><PERSON> (MP), cyan (faible)", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "<PERSON> (HP), <PERSON><PERSON><PERSON> (MP), <PERSON><PERSON><PERSON> (indications)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "Alarmes priorité haute (HP) et moyenne (MP)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "Alarmes priorité haute (HP) et moyenne (MP)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "<PERSON> (HP), <PERSON><PERSON><PERSON> (MP), <PERSON><PERSON><PERSON> (indications)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "Alarmes priorité haute (HP) et moyenne (MP)", "fulfills_requirement": true}]}, {"spec_name": "Défaut d'alimentation en gaz", "cps_spec_requirement": "Défaut d'alimentation en gaz.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "\"Entrée air\", \"Entrée oxygène\", \"Arrivée gaz\"", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "'O2 pression basse''", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "'O2 pression basse''", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "'O2 pression basse''", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "'O2 pression basse''", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "'O2 pression basse''", "fulfills_requirement": true}]}, {"spec_name": "Défaut d'alimentation électrique", "cps_spec_requirement": "Défaut d’alimentation électrique.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Batterie faible", "cps_spec_requirement": "Batterie faible (indicateur de niveau de charge sur l’écran)", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Concentration en CO2", "cps_spec_requirement": "Concentration en 02", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "EtCO2 haute/basse", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "EtCO2 haute/basse", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "EtCO2 haute/basse", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "EtCO2 haute/basse", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Volume minute bas", "cps_spec_requirement": "Volume minute bas.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Volume minute haut", "cps_spec_requirement": "Volume minute haut.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Pression inspiratoire basse", "cps_spec_requirement": "Pression inspiratoire basse", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Apnée", "cps_spec_requirement": "Apnée", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Pression élvée en permanence ", "cps_spec_requirement": "Pression élevée en permanence.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "'Pression ventilation haute''", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "'Pression ventilation haute''", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "'Pression ventilation haute''", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "'Pression ventilation haute''", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "'Pression ventilation haute''", "fulfills_requirement": true}]}, {"spec_name": "Fréquence respiratoire haute", "cps_spec_requirement": "Fréquence respiratoire haute.", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "<PERSON><PERSON><PERSON> respiratoire basse", "cps_spec_requirement": "<PERSON><PERSON><PERSON> respiratoire basse", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Alarmes audio-visuelles", "cps_spec_requirement": "Sonore et visuelle avec des indicateurs de la priorité (Haute, Moyenne et Basse)", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "<PERSON><PERSON> (47,5-70 dB)", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "<PERSON><PERSON> (46-56 dB)", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "<PERSON><PERSON> (72-80 dB)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "<PERSON><PERSON> (72-80 dB)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "<PERSON><PERSON> (72-80 dB)", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "<PERSON><PERSON> (58-68 dB)", "fulfills_requirement": true}]}], "Accessoires : (oui /non/option)": [{"spec_name": "Nébuliseur", "cps_spec_requirement": "Nébulisation des médicaments", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "SOPHIE", "product_value": "Nébuliseur pneumatique de médicaments", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "Activation 5-30 min,  Nébuliseur pneumatique de médicaments 22m/22f", "fulfills_requirement": true}]}, {"spec_name": "Compensation automatique de la résistance du tube", "cps_spec_requirement": "Compensation automatique de la sonde d'intubation", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "0-100%", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "0 à 100 %", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "0 à 100 %", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "0 à 100 %", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "1 à 100 %", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "0 à 100 %", "fulfills_requirement": true}]}, {"spec_name": "Auto test", "cps_spec_requirement": "Autotest du bon fonctionnement", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "Suite à la mise sous tension, l'appareil EVA exécute un autotest automatique", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Humidifica<PERSON><PERSON> chauffant", "cps_spec_requirement": "Humidificateur", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "Option", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "<PERSON><PERSON>, Intégré à l'unité patient P7", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "Option", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "Option", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "Option", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "Option", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "Option", "fulfills_requirement": true}]}, {"spec_name": "Chariot mobile d'origine", "cps_spec_requirement": "Chariot mobile ergonomique d’origine", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "EVE NEO", "product_value": "O<PERSON>", "fulfills_requirement": true}]}, {"spec_name": "Bras articulé  supportant circuit patient", "cps_spec_requirement": "Bras articule support circuit patient", "products_evaluation": [{"product_name": "Monnal TEO", "product_value": "O<PERSON>", "fulfills_requirement": true}, {"product_name": "SOPHIE", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVA", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE", "product_value": "<PERSON><PERSON> (option)", "fulfills_requirement": true}, {"product_name": "EVE IN", "product_value": "<PERSON><PERSON> (option)", "fulfills_requirement": true}, {"product_name": "EVA NEO", "product_value": "Non", "fulfills_requirement": false}, {"product_name": "EVE NEO", "product_value": "Non", "fulfills_requirement": false}]}]}, "non_matched_criterias": [{"spec_name": "Monitorage et surveillance : Courbes en temps réel : pression, débit et volume", "cps_spec_requirement": "Courbes en temps réel : pression, débit et volume"}, {"spec_name": "Monitorage et surveillance : Boucles : volume/pression et Débit/volume", "cps_spec_requirement": "Boucles : volume/pression et Débit/volume"}, {"spec_name": "Monitorage et surveillance : Pression des voies aériennes, pression Crête, pression plateau, PEP", "cps_spec_requirement": "Pression des voies aériennes, pression Crête, pression plateau, PEP"}, {"spec_name": "Monitorage et surveillance : Oxygène, volume, résistance, activité de patient", "cps_spec_requirement": "Oxygène, volume, résistance, activité de patient"}, {"spec_name": "Monitorage et surveillance : Affichage de poumon dynamique avec représentation de la compliance pulmonaire", "cps_spec_requirement": "Affichage de poumon dynamique avec représentation de la compliance pulmonaire"}, {"spec_name": "Fréquence respiratoire : de 1 à 80 cycles/min minimum", "cps_spec_requirement": "Fréquence respiratoire : de 1 à 80 cycles/min minimum"}, {"spec_name": "Autres fonctions : L’appareil doit permettre l’enregistrement des tendances de 1 à 72 heures", "cps_spec_requirement": "Le respirateur doit permettre l’enregistrement des tendances de 1 à 72 heures"}, {"spec_name": "L’appareil doit être livré avec : Accessoires complets pour nébulisation", "cps_spec_requirement": "Accessoires complets pour nébulisation"}, {"spec_name": "L’appareil doit être livré avec : 2 circuits patient adulte et enfant réutilisable", "cps_spec_requirement": "2 circuits patient adulte et enfant réutilisable"}, {"spec_name": "L’appareil doit être livré avec : 10 filtres anti bactériens", "cps_spec_requirement": "10 filtres anti bactériens"}, {"spec_name": "L’appareil doit être livré avec : 10 kits pour l'oxygénothérapie", "cps_spec_requirement": "10 kits pour l'oxygénothérapie"}, {"spec_name": "L’appareil doit être livré avec : 2 Capteurs de débit adulte autoclavable", "cps_spec_requirement": "2 Capteurs de débit adulte autoclavable"}, {"spec_name": "L’appareil doit être livré avec : 2 Capteurs de débit enfant autoclavable", "cps_spec_requirement": "2 Capteurs de débit enfant autoclavable"}, {"spec_name": "L’appareil doit être livré avec : 01 Capteur CO2 réutilisable", "cps_spec_requirement": "01 Capteur CO2 réutilisable"}, {"spec_name": "Le respirateur de transport doit être avec un système à turbine intégré qui ne nécessite pas l'alimentation en AIR", "cps_spec_requirement": "Le respirateur de transport doit être avec un système à turbine intégré qui ne nécessite pas l'alimentation en AIR"}]}, "matched_specs_count": 41, "non_matched_specs_count": 15, "output_format": "standard", "message": "Specification analysis completed using standard format. Only matched and non-matched criteria are returned."}