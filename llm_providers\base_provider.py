from abc import ABC, abstractmethod

class BaseLLMProvider(ABC):
    """Base class for LLM providers"""

    @abstractmethod
    def generate_response(self, prompt, temperature=0, max_tokens=30000):
        """
        Generate a response from the LLM

        Args:
            prompt (str): The prompt to send to the LLM
            temperature (float): The temperature for generation (default: 0 for more deterministic outputs)
            max_tokens (int): Maximum number of tokens to generate (default: 1024 for more detailed responses)

        Returns:
            str: The generated response
        """
        pass

    @abstractmethod
    def parse_specifications(self, specifications_text):
        """
        Parse product specifications text into structured format

        Args:
            specifications_text (str): Raw specifications text

        Returns:
            dict: Parsed specifications
        """
        pass

    @abstractmethod
    def compare_specifications(self, parsed_specs, product_specs):
        """
        Compare parsed specifications with a product's specifications

        Args:
            parsed_specs (dict): Parsed specifications
            product_specs (dict): Product specifications

        Returns:
            dict: Comparison results with reasoning
        """
        pass
