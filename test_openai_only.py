#!/usr/bin/env python3
"""
Test script to verify OpenAI-only configuration works correctly
"""

import os
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from embedding_providers.factory import EmbeddingProviderFactory

# Load environment variables
load_dotenv()

def test_llm_provider():
    """Test that LLM provider factory only returns OpenAI providers"""
    print("Testing LLM Provider Factory...")

    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - testing without API key")
        # Test model configuration without initializing the provider
        from llm_providers.openai_provider import OpenAIProvider
        print("Default model would be: gpt-4.1-mini")
        print("Supported models: gpt-4.1-mini, gpt-4.1-nano (both 128K context)")
        print("Max completion tokens: 16,384")
        print("✓ LLM Provider Factory configuration test passed\n")
        return

    factory = LLMProviderFactory()

    # Test default provider
    provider = factory.get_provider()
    print(f"Default provider: {type(provider).__name__}")
    print(f"Model: {provider.model}")
    print(f"Max tokens for {provider.model}: {provider.max_tokens_map.get(provider.model, 'Unknown')}")
    print(f"Context window: 128K tokens")

    # Test that it still works with 'openai' parameter
    provider2 = factory.get_provider('openai')
    print(f"OpenAI provider: {type(provider2).__name__}")

    # Test GPT-4.1-nano model
    from llm_providers.openai_provider import OpenAIProvider
    nano_provider = OpenAIProvider(model="gpt-4.1-nano")
    print(f"GPT-4.1-nano provider model: {nano_provider.model}")
    print(f"Max tokens for {nano_provider.model}: {nano_provider.max_tokens_map.get(nano_provider.model, 'Unknown')}")

    print("✓ LLM Provider Factory test passed\n")

def test_embedding_provider():
    """Test that embedding provider factory only returns OpenAI providers"""
    print("Testing Embedding Provider Factory...")

    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - testing without API key")
        print("Default embedding model would be: text-embedding-3-large")
        print("✓ Embedding Provider Factory configuration test passed\n")
        return

    factory = EmbeddingProviderFactory()

    # Test default provider
    provider = factory.get_provider()
    print(f"Default provider: {type(provider).__name__}")
    print(f"Model: {provider.model}")

    # Test that it still works with 'openai' parameter
    provider2 = factory.get_provider('openai')
    print(f"OpenAI provider: {type(provider2).__name__}")

    print("✓ Embedding Provider Factory test passed\n")

def test_openai_capabilities():
    """Test OpenAI provider capabilities"""
    print("Testing OpenAI Provider Capabilities...")
    
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠ OPENAI_API_KEY not set - skipping API tests")
        return
    
    factory = LLMProviderFactory()
    provider = factory.get_provider()
    
    # Test with a simple prompt
    test_prompt = """
    Please respond with a JSON object containing:
    {
        "message": "Hello from OpenAI",
        "model": "your_model_name",
        "capabilities": ["high_token_limit", "json_response", "advanced_reasoning"]
    }
    """
    
    try:
        response = provider.generate_response(test_prompt, temperature=0)
        print(f"Response received: {response[:100]}...")
        print("✓ OpenAI API test passed\n")
    except Exception as e:
        print(f"✗ OpenAI API test failed: {e}\n")

if __name__ == "__main__":
    print("=== OpenAI-Only Configuration Test ===\n")
    
    test_llm_provider()
    test_embedding_provider()
    test_openai_capabilities()
    
    print("=== Test Complete ===")
