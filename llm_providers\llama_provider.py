import os
import json
import requests
from openai import OpenAI
from .base_provider import BaseLLMProvider

class LlamaProvider(BaseLLMProvider):
    """Llama LLM provider implementation using OVH API"""

    def __init__(self, model="Llama-3.1-8B-Instruct"):
        """
        Initialize Llama provider

        Args:
            model (str): The Llama model to use
        """
        self.api_url = os.getenv("LLAMA_API_URL")
        if not self.api_url:
            raise ValueError("LLAMA_API_URL environment variable not set")

        self.token = os.getenv("OVH_TOKEN")
        if not self.token:
            raise ValueError("OVH_TOKEN environment variable not set")

        self.model = model
        self.client = OpenAI(
            base_url=self.api_url,
            api_key=self.token
        )

    def generate_response(self, prompt, temperature=0, max_tokens=30000):
        """
        Generate a response from Llama using OVH API

        Args:
            prompt (str): The prompt to send to the LLM
            temperature (float): The temperature for generation
            max_tokens (int): Maximum number of tokens to generate

        Returns:
            str: The generated response
        """
        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[{"role": "user", "content": prompt}],
                temperature=temperature,
                response_format={ "type": "json_object" },
                max_tokens=max_tokens
            )

            return response.choices[0].message.content
        except Exception as e:
            print(f"Error generating response from Llama: {e}")
            return f"Error: {str(e)}"

    def parse_specifications(self, specifications_text):
        """
        Parse product specifications text into structured format using Llama

        Args:
            specifications_text (str): Raw specifications text

        Returns:
            dict: Parsed specifications
        """
        prompt = f"""
        Parse the following product specifications into a structured format.
        Extract each specification criterion and its value.

        Specifications:
        {specifications_text}

        Return the result as a JSON object with criteria as keys and values as values.
        Only include criteria that are explicitly mentioned in the specifications.
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, try to parse the text manually
                parsed_specs = {}
                lines = specifications_text.strip().split('\n')
                for line in lines:
                    if ':' in line:
                        key, value = line.split(':', 1)
                        parsed_specs[key.strip()] = value.strip()
                return parsed_specs
        except Exception as e:
            print(f"Error parsing specifications: {e}")
            return {}

    def compare_specifications(self, parsed_specs, product_specs):
        """
        Compare parsed specifications with a product's specifications using Llama

        Args:
            parsed_specs (dict): Parsed specifications
            product_specs (dict): Product specifications

        Returns:
            dict: Comparison results with reasoning
        """
        prompt = f"""
        Compare the following specifications with a product's specifications.

        User Specifications:
        {json.dumps(parsed_specs, ensure_ascii=False, indent=2)}

        Product Specifications:
        {json.dumps(product_specs, ensure_ascii=False, indent=2)}

        For each criterion in the user specifications, determine if it matches the product specifications.
        Provide detailed reasoning for each comparison.

        Return the result as a JSON object with the following structure:
        {{
            "comparisons": [
                {{
                    "criterion": "criterion_name",
                    "user_value": "user_value",
                    "product_value": "product_value",
                    "match": true/false,
                    "reasoning": "detailed reasoning"
                }},
                ...
            ],
            "match_count": number_of_matches,
            "total_count": total_number_of_criteria,
            "match_percentage": percentage_of_matches
        }}
        """

        response = self.generate_response(prompt)

        try:
            # Try to extract JSON from the response
            json_start = response.find('{')
            json_end = response.rfind('}') + 1
            if json_start >= 0 and json_end > json_start:
                json_str = response[json_start:json_end]
                return json.loads(json_str)
            else:
                # If no JSON found, return a basic structure
                return {
                    "comparisons": [],
                    "match_count": 0,
                    "total_count": len(parsed_specs),
                    "match_percentage": 0
                }
        except Exception as e:
            print(f"Error comparing specifications: {e}")
            return {
                "comparisons": [],
                "match_count": 0,
                "total_count": len(parsed_specs),
                "match_percentage": 0,
                "error": str(e)
            }
