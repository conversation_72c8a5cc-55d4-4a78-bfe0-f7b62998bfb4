from flask import Flask, request, jsonify, render_template
import os
from dotenv import load_dotenv
from llm_providers.provider_factory import LLMProviderFactory
from embedding_providers.factory import EmbeddingProviderFactory
from product_comparison.intelligent_parser import IntelligentSpecificationParser
from product_comparison.embedding_comparator import EmbeddingBasedComparator
from product_comparison.spec_selector import EmbeddingSpecificationSelector
from product_comparison.data_loader import load_available_products

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize OpenAI providers (only OpenAI is supported)
llm_factory = LLMProviderFactory()
embedding_factory = EmbeddingProviderFactory()

# Load available products data
available_products = load_available_products('available_products.json')

# Path to comprehensive comparison data
comparison_data_path = 'product_comparison_data.json'

# Initialize embedding selector with OpenAI embeddings
embedding_provider = embedding_factory.get_provider()
embedding_selector = EmbeddingSpecificationSelector(
    embedding_provider,
    similarity_threshold=float(os.getenv('EMBEDDING_SIMILARITY_THRESHOLD', '0.6')),
    embeddings_file=os.getenv('EMBEDDINGS_FILE', 'spec_embeddings.pkl')
)
embedding_selector.load_simplified_specs()
embedding_selector.generate_embeddings()

@app.route('/')
def index():
    """Render the main page"""
    return render_template('index.html')

@app.route('/api/compare', methods=['POST'])
def compare_specifications():
    """API endpoint to compare specifications with available products using OpenAI"""
    data = request.json

    # Get input specifications
    specifications = data.get('specifications', '')
    use_embeddings = data.get('use_embeddings', True)  # Default to embedding-based comparison

    # Get OpenAI LLM provider
    llm_provider = llm_factory.get_provider()

    # If using embeddings approach (recommended)
    if use_embeddings:
        # Use embedding-based comparison with OpenAI
        comparator = EmbeddingBasedComparator(llm_provider, embedding_selector)
        results = comparator.compare(specifications, available_products)

    # Fallback to previous approaches if embeddings not used
    else:
        # Use intelligent parsing with products_specs.json knowledge
        intelligent_parser = IntelligentSpecificationParser(llm_provider)
        parsing_result = intelligent_parser.parse(specifications)

        # Return only parsing results (no product comparison)
        results = {
            "parsing_result": parsing_result,
            "matched_specs_count": sum(len(specs) for specs in parsing_result.get("matched_criterias", {}).values()),
            "non_matched_specs_count": len(parsing_result.get("non_matched_criterias", [])),
            "message": "Specification analysis completed. Only matched and non-matched criteria are returned."
        }

    return jsonify(results)

if __name__ == '__main__':
    app.run(debug=True)
