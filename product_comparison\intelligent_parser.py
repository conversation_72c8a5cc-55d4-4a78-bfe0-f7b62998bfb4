import json
import os

class IntelligentSpecificationParser:
    """
    Intelligent parser for product specifications using OpenAI and products_specs.json
    Replaces regex/comma separation logic with AI-powered extraction
    """

    def __init__(self, llm_provider, products_specs_path='products_specs.json', chunk_size=10, include_reasoning=False, output_format="mini"):
        """
        Initialize the intelligent parser

        Args:
            llm_provider: The LLM provider to use for parsing
            products_specs_path (str): Path to products_specs.json file
            chunk_size (int): Number of specifications per API call (default: 10, -1 for all)
            include_reasoning (bool): Whether to include reasoning in output (default: False)
            output_format (str): "standard" or "mini" (default: "mini")
        """
        self.llm_provider = llm_provider
        self.products_specs_path = products_specs_path
        self.chunk_size = chunk_size
        self.include_reasoning = include_reasoning
        self.output_format = output_format
        self.products_specs_content = None
        self._load_products_specs()

    def _load_products_specs(self):
        """Load products specifications from JSON file"""
        try:
            with open(self.products_specs_path, 'r', encoding='utf-8') as f:
                self.products_specs_content = json.load(f)

            # Count total specifications
            total_specs = 0
            for group_dict in self.products_specs_content.get('groups', []):
                for category_name, specs_list in group_dict.items():
                    total_specs += len(specs_list)

            print(f"Loaded products specs with {len(self.products_specs_content.get('groups', []))} categories and {total_specs} specifications")
        except Exception as e:
            print(f"Error loading products specs: {e}")
            self.products_specs_content = {"groups": []}
    
    def parse(self, specifications_text):
        """
        Parse product specifications text using intelligent OpenAI-powered extraction

        Args:
            specifications_text (str): Raw specifications text from user

        Returns:
            dict: Dictionary with 'matched_criterias' and 'non_matched_criterias'
        """
        if not self.products_specs_content:
            print("Warning: No products specs loaded, falling back to basic parsing")
            return self._fallback_parse(specifications_text)

        # Use the intelligent parsing method with chunking
        result = self.llm_provider.intelligent_parse_specifications(
            specifications_text,
            self.products_specs_content,
            self.chunk_size,
            self.include_reasoning,
            self.output_format
        )

        # Validate and clean the result
        validated_result = self._validate_and_clean_result(result)

        # Print summary for debugging
        self._print_parsing_summary(validated_result)

        return validated_result
    
    def _validate_and_clean_result(self, result):
        """
        Validate and clean the parsing result
        
        Args:
            result (dict): Raw result from LLM
            
        Returns:
            dict: Validated and cleaned result
        """
        if not isinstance(result, dict):
            return {"matched_criterias": {}, "non_matched_criterias": []}
        
        # Ensure required keys exist
        matched = result.get("matched_criterias", {})
        non_matched = result.get("non_matched_criterias", [])
        
        # Validate matched_criterias structure
        if not isinstance(matched, dict):
            matched = {}
        
        # Validate non_matched_criterias structure
        if not isinstance(non_matched, list):
            non_matched = []
        
        # Clean and validate each matched category
        cleaned_matched = {}
        for category, specs in matched.items():
            if isinstance(specs, list):
                cleaned_specs = []
                for spec in specs:
                    if isinstance(spec, dict) and 'spec_name' in spec:
                        spec_data = {
                            'spec_name': spec.get('spec_name', ''),
                            'cps_spec_requirement': spec.get('cps_spec_requirement', '')
                        }

                        if self.output_format == "mini":
                            # Handle mini format with products_fulfills_requirement
                            products_fulfills = spec.get('products_fulfills_requirement', [])
                            if not isinstance(products_fulfills, list):
                                products_fulfills = []
                            spec_data['products_fulfills_requirement'] = products_fulfills
                        else:
                            # Handle standard format with products_evaluation
                            products_evaluation = spec.get('products_evaluation', [])
                            if not isinstance(products_evaluation, list):
                                products_evaluation = []

                            cleaned_products = []
                            for product_eval in products_evaluation:
                                if isinstance(product_eval, dict):
                                    product_data = {
                                        'product_name': product_eval.get('product_name', ''),
                                        'product_value': product_eval.get('product_value', ''),
                                        'fulfills_requirement': product_eval.get('fulfills_requirement', False)
                                    }
                                    # Only include reasoning if it exists and include_reasoning is True
                                    if self.include_reasoning and 'reasoning' in product_eval:
                                        product_data['reasoning'] = product_eval.get('reasoning', '')
                                    cleaned_products.append(product_data)

                            spec_data['products_evaluation'] = cleaned_products

                        cleaned_specs.append(spec_data)
                if cleaned_specs:
                    cleaned_matched[category] = cleaned_specs
        
        # Clean and validate non_matched_criterias
        cleaned_non_matched = []
        for spec in non_matched:
            if isinstance(spec, dict) and 'spec_name' in spec:
                cleaned_non_matched.append({
                    'spec_name': spec.get('spec_name', ''),
                    'cps_spec_requirement': spec.get('cps_spec_requirement', '')
                })
        
        return {
            "matched_criterias": cleaned_matched,
            "non_matched_criterias": cleaned_non_matched
        }
    
    def _print_parsing_summary(self, result):
        """Print a summary of the parsing results"""
        matched = result.get("matched_criterias", {})
        non_matched = result.get("non_matched_criterias", [])
        
        total_matched = sum(len(specs) for specs in matched.values())
        total_non_matched = len(non_matched)
        
        print(f"\n=== Intelligent Parsing Summary ===")
        print(f"Matched specifications: {total_matched}")
        print(f"Non-matched specifications: {total_non_matched}")
        
        if matched:
            print("\nMatched by category:")
            for category, specs in matched.items():
                print(f"  {category}: {len(specs)} specs")
                for spec in specs[:3]:  # Show first 3 specs
                    if self.output_format == "mini":
                        products_fulfills = spec.get('products_fulfills_requirement', [])
                        print(f"    - {spec['spec_name']}: {len(products_fulfills)} products fulfill requirement")
                        print(f"      Fulfilling products: {', '.join(products_fulfills[:3])}{'...' if len(products_fulfills) > 3 else ''}")
                    else:
                        products_evaluation = spec.get('products_evaluation', [])
                        fulfills_count = sum(1 for p in products_evaluation if p.get('fulfills_requirement', False))
                        total_products = len(products_evaluation)
                        print(f"    - {spec['spec_name']}: {fulfills_count}/{total_products} products fulfill requirement")
                    print(f"      Requirement: {spec.get('cps_spec_requirement', 'N/A')}")
                if len(specs) > 3:
                    print(f"    ... and {len(specs) - 3} more")
        
        if non_matched:
            print(f"\nNon-matched specifications:")
            for spec in non_matched[:5]:  # Show first 5 non-matched
                print(f"  - {spec['spec_name']}")
            if len(non_matched) > 5:
                print(f"  ... and {len(non_matched) - 5} more")
        print("=" * 35)
    
    def _fallback_parse(self, specifications_text):
        """
        Fallback parsing method if simplified specs are not available
        
        Args:
            specifications_text (str): Raw specifications text
            
        Returns:
            dict: Basic parsed result
        """
        # Use the basic parsing method
        basic_result = self.llm_provider.parse_specifications(specifications_text)
        
        # Convert to the new format
        non_matched = []
        for key, value in basic_result.items():
            non_matched.append({
                'spec_name': key,
                'cps_spec_requirement': f"{key}: {value}"
            })
        
        return {
            "matched_criterias": {},
            "non_matched_criterias": non_matched
        }
    

