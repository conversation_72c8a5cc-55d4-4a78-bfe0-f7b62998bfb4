import os
import numpy as np
from .base_provider import BaseEmbeddingProvider

class SentenceTransformerEmbeddingProvider(BaseEmbeddingProvider):
    """Sentence Transformer embedding provider implementation"""

    def __init__(self, model_name="all-MiniLM-L6-v2"):
        """
        Initialize Sentence Transformer embedding provider
        
        Args:
            model_name (str): The Sentence Transformer model to use
        """
        try:
            from sentence_transformers import SentenceTransformer
            self.model = SentenceTransformer(model_name)
        except ImportError:
            raise ImportError("Please install sentence-transformers: pip install sentence-transformers")
    
    def get_embeddings(self, texts):
        """
        Get embeddings for a list of texts using Sentence Transformers
        
        Args:
            texts (list): List of text strings to embed
            
        Returns:
            list: List of embedding vectors
        """
        if not texts:
            return []
        
        try:
            # Encode the texts to get embeddings
            embeddings = self.model.encode(texts, convert_to_tensor=False)
            return embeddings.tolist()
        
        except Exception as e:
            print(f"Error getting embeddings from Sentence Transformers: {e}")
            return [[] for _ in texts]  # Return empty embeddings on error
    
    def compute_similarity(self, embedding1, embedding2):
        """
        Compute cosine similarity between two embeddings
        
        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector
            
        Returns:
            float: Cosine similarity score (higher means more similar)
        """
        if not embedding1 or not embedding2:
            return 0.0
        
        # Convert to numpy arrays
        vec1 = np.array(embedding1)
        vec2 = np.array(embedding2)
        
        # Compute cosine similarity
        dot_product = np.dot(vec1, vec2)
        norm1 = np.linalg.norm(vec1)
        norm2 = np.linalg.norm(vec2)
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
        
        return dot_product / (norm1 * norm2)
