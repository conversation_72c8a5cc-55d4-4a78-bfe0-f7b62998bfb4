class ProductComparator:
    """Comparator for product specifications"""
    
    def __init__(self, llm_provider):
        """
        Initialize the comparator
        
        Args:
            llm_provider: The LLM provider to use for comparison
        """
        self.llm_provider = llm_provider
    
    def compare(self, parsed_specs, available_products):
        """
        Compare parsed specifications with available products
        
        Args:
            parsed_specs (dict): Parsed specifications
            available_products (list): List of available products
            
        Returns:
            dict: Comparison results
        """
        results = {
            "products": [],
            "best_match": None,
            "best_match_score": 0,
            "detailed_analysis": {}
        }
        
        for product in available_products:
            product_name = product.get('Nom', '')

            # Extract only specification fields (exclude 'Nom' and 'Description')
            product_specs = {k: v for k, v in product.items()
                           if k not in ['Nom', 'Description']}

            # Compare specifications with this product
            comparison = self.llm_provider.compare_specifications(parsed_specs, product_specs)

            # Add product comparison to results
            product_result = {
                "name": product_name,
                "description": product.get('Description', ''),
                "match_count": comparison.get('match_count', 0),
                "total_count": comparison.get('total_count', 0),
                "match_percentage": comparison.get('match_percentage', 0),
                "comparisons": comparison.get('comparisons', [])
            }
            
            results["products"].append(product_result)
            
            # Update best match if this product has a higher match percentage
            if product_result["match_percentage"] > results["best_match_score"]:
                results["best_match"] = product_name
                results["best_match_score"] = product_result["match_percentage"]
            
            # Store detailed analysis
            results["detailed_analysis"][product_name] = comparison
        
        return results
