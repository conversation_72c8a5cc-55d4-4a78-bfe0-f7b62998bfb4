#!/usr/bin/env python3
"""
Test the Flask API endpoint to verify the intelligent parsing works
"""

import requests
import json
import time

def test_api_endpoint():
    """Test the API endpoint with intelligent parsing"""
    
    # Wait a moment for Flask to fully start
    time.sleep(2)
    
    print("=== Testing Flask API Endpoint ===\n")
    
    # Test data
    test_payload = {
        "specifications": """
        Volume controlé: Oui
        Pression controlée: Oui
        Aide inspiratoire (AI): Oui
        HFO: Non
        Custom feature: Required
        """,
        "use_embeddings": False,  # Use intelligent parsing
        "use_chunked": False
    }
    
    print("Test payload:")
    print(f"  Specifications: {test_payload['specifications'].strip()}")
    print(f"  Use embeddings: {test_payload['use_embeddings']}")
    print()
    
    try:
        # Make API request
        print("Making API request...")
        response = requests.post(
            'http://localhost:5000/api/compare', 
            json=test_payload,
            timeout=30
        )
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            
            # Check parsing results
            parsing_result = data.get('parsing_result', {})
            matched_count = data.get('matched_specs_count', 0)
            non_matched_count = data.get('non_matched_specs_count', 0)
            
            print(f"✅ API request successful!")
            print(f"   Matched specifications: {matched_count}")
            print(f"   Non-matched specifications: {non_matched_count}")
            
            # Check comparison results
            products = data.get('products', [])
            best_match = data.get('best_match', 'None')
            best_score = data.get('best_match_score', 0)
            
            print(f"   Products compared: {len(products)}")
            print(f"   Best match: {best_match}")
            print(f"   Best score: {best_score}%")
            
            if products:
                print("\n   Top 3 products:")
                sorted_products = sorted(products, key=lambda x: x.get('match_percentage', 0), reverse=True)
                for i, product in enumerate(sorted_products[:3], 1):
                    print(f"     {i}. {product['name']}: {product['match_percentage']}%")
            
            # Check parsing details
            if parsing_result:
                matched_criterias = parsing_result.get('matched_criterias', {})
                non_matched_criterias = parsing_result.get('non_matched_criterias', [])
                
                print(f"\n   Parsing details:")
                print(f"     Matched categories: {len(matched_criterias)}")
                for category, specs in matched_criterias.items():
                    print(f"       {category}: {len(specs)} specs")
                
                if non_matched_criterias:
                    print(f"     Non-matched specs:")
                    for spec in non_matched_criterias:
                        print(f"       - {spec.get('spec_name', 'Unknown')}")
            
            print("\n✅ All tests passed! The intelligent parsing is working correctly.")
            
        else:
            print(f"❌ API request failed with status {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to Flask app. Make sure it's running on http://localhost:5000")
    except requests.exceptions.Timeout:
        print("❌ Request timed out. The API might be taking too long to respond.")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def test_embedding_mode():
    """Test the embedding mode for comparison"""
    
    print("\n=== Testing Embedding Mode ===\n")
    
    test_payload = {
        "specifications": "Volume controlé: Oui, Pression controlée: Oui",
        "use_embeddings": True,  # Use embedding-based comparison
        "use_chunked": False
    }
    
    try:
        response = requests.post(
            'http://localhost:5000/api/compare', 
            json=test_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            products = data.get('products', [])
            selected_specs = data.get('selected_specs', {})
            
            print(f"✅ Embedding mode works!")
            print(f"   Selected specs categories: {len(selected_specs)}")
            print(f"   Products compared: {len(products)}")
            
        else:
            print(f"❌ Embedding mode failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Embedding mode error: {e}")

if __name__ == "__main__":
    test_api_endpoint()
    test_embedding_mode()
    
    print(f"\n{'='*60}")
    print("API testing complete!")
    print("If tests passed, the web interface should work correctly.")
    print(f"{'='*60}")
